//  A convenience header for Boost.Signals2, should pull in everying in the library.

//  Copyright (c) 2008-2009 <PERSON>

// Use, modification and
// distribution is subject to the Boost Software License, Version
// 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_SIGNALS2_HPP
#define BOOST_SIGNALS2_HPP

// For documentation, see http://www.boost.org/libs/signals2/

#include <boost/signals2/deconstruct.hpp>
#include <boost/signals2/deconstruct_ptr.hpp>
#include <boost/signals2/dummy_mutex.hpp>
#include <boost/signals2/last_value.hpp>
#include <boost/signals2/signal.hpp>
#include <boost/signals2/signal_type.hpp>
#include <boost/signals2/shared_connection_block.hpp>

#endif
