<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0F71334B-8B28-4106-A8A5-9E4F46B195EE}</ProjectGuid>
    <RootNamespace>boost</RootNamespace>
    <WindowsTargetPlatformVersion>
    </WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140_xp</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140_xp</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IncludePath>$(VCInstallDir)atlmfc\include;$(VCInstallDir)include;$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</IncludePath>
    <OutDir>$(SolutionDir)$(Configuration)\lib\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\lib\</OutDir>
    <IncludePath>$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;XLOGGER_TAG="mars::$(TargetName)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/Zc:threadSafeInit- %(AdditionalOptions)</AdditionalOptions>
      <AdditionalIncludeDirectories>$(ProjectDir)../;$(ProjectDir)../comm/windows/</AdditionalIncludeDirectories>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>BOOST_NO_EXCEPTIONS;XLOGGER_TAG="mars::$(TargetName)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir)../;$(ProjectDir)../comm/windows/</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>/Zc:threadSafeInit- %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="algorithm\all_of.hpp" />
    <ClInclude Include="algorithm\any_of.hpp" />
    <ClInclude Include="algorithm\copy_if.hpp" />
    <ClInclude Include="algorithm\copy_n.hpp" />
    <ClInclude Include="algorithm\find_if_not.hpp" />
    <ClInclude Include="algorithm\iota.hpp" />
    <ClInclude Include="algorithm\is_partitioned.hpp" />
    <ClInclude Include="algorithm\is_permutation.hpp" />
    <ClInclude Include="algorithm\is_sorted.hpp" />
    <ClInclude Include="algorithm\none_of.hpp" />
    <ClInclude Include="algorithm\one_of.hpp" />
    <ClInclude Include="algorithm\partition_copy.hpp" />
    <ClInclude Include="algorithm\partition_point.hpp" />
    <ClInclude Include="algorithm\string\classification.hpp" />
    <ClInclude Include="algorithm\string\compare.hpp" />
    <ClInclude Include="algorithm\string\concept.hpp" />
    <ClInclude Include="algorithm\string\config.hpp" />
    <ClInclude Include="algorithm\string\constants.hpp" />
    <ClInclude Include="algorithm\string\detail\classification.hpp" />
    <ClInclude Include="algorithm\string\detail\finder.hpp" />
    <ClInclude Include="algorithm\string\detail\find_iterator.hpp" />
    <ClInclude Include="algorithm\string\detail\trim.hpp" />
    <ClInclude Include="algorithm\string\detail\util.hpp" />
    <ClInclude Include="algorithm\string\finder.hpp" />
    <ClInclude Include="algorithm\string\find_iterator.hpp" />
    <ClInclude Include="algorithm\string\iter_find.hpp" />
    <ClInclude Include="algorithm\string\predicate_facade.hpp" />
    <ClInclude Include="algorithm\string\split.hpp" />
    <ClInclude Include="algorithm\string\trim.hpp" />
    <ClInclude Include="aligned_storage.hpp" />
    <ClInclude Include="align\align.hpp" />
    <ClInclude Include="align\detail\address.hpp" />
    <ClInclude Include="align\detail\align.hpp" />
    <ClInclude Include="align\detail\align_cxx11.hpp" />
    <ClInclude Include="align\detail\is_alignment.hpp" />
    <ClInclude Include="any.hpp" />
    <ClInclude Include="array.hpp" />
    <ClInclude Include="assert.hpp" />
    <ClInclude Include="atomic.hpp" />
    <ClInclude Include="atomic\atomic.hpp" />
    <ClInclude Include="atomic\atomic_flag.hpp" />
    <ClInclude Include="atomic\capabilities.hpp" />
    <ClInclude Include="atomic\detail\atomic_flag.hpp" />
    <ClInclude Include="atomic\detail\atomic_template.hpp" />
    <ClInclude Include="atomic\detail\bitwise_cast.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_alpha.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_arm.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_atomic.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_ppc.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_sparc.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_sync.hpp" />
    <ClInclude Include="atomic\detail\caps_gcc_x86.hpp" />
    <ClInclude Include="atomic\detail\caps_linux_arm.hpp" />
    <ClInclude Include="atomic\detail\caps_msvc_arm.hpp" />
    <ClInclude Include="atomic\detail\caps_msvc_x86.hpp" />
    <ClInclude Include="atomic\detail\caps_windows.hpp" />
    <ClInclude Include="atomic\detail\config.hpp" />
    <ClInclude Include="atomic\detail\interlocked.hpp" />
    <ClInclude Include="atomic\detail\int_sizes.hpp" />
    <ClInclude Include="atomic\detail\link.hpp" />
    <ClInclude Include="atomic\detail\lockpool.hpp" />
    <ClInclude Include="atomic\detail\operations.hpp" />
    <ClInclude Include="atomic\detail\operations_fwd.hpp" />
    <ClInclude Include="atomic\detail\operations_lockfree.hpp" />
    <ClInclude Include="atomic\detail\ops_cas_based.hpp" />
    <ClInclude Include="atomic\detail\ops_emulated.hpp" />
    <ClInclude Include="atomic\detail\ops_extending_cas_based.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_alpha.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_arm.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_atomic.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_ppc.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_sparc.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_sync.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_x86.hpp" />
    <ClInclude Include="atomic\detail\ops_gcc_x86_dcas.hpp" />
    <ClInclude Include="atomic\detail\ops_linux_arm.hpp" />
    <ClInclude Include="atomic\detail\ops_msvc_arm.hpp" />
    <ClInclude Include="atomic\detail\ops_msvc_common.hpp" />
    <ClInclude Include="atomic\detail\ops_msvc_x86.hpp" />
    <ClInclude Include="atomic\detail\ops_windows.hpp" />
    <ClInclude Include="atomic\detail\pause.hpp" />
    <ClInclude Include="atomic\detail\platform.hpp" />
    <ClInclude Include="atomic\detail\storage_type.hpp" />
    <ClInclude Include="atomic\fences.hpp" />
    <ClInclude Include="bind.hpp" />
    <ClInclude Include="bind\arg.hpp" />
    <ClInclude Include="bind\bind.hpp" />
    <ClInclude Include="bind\bind_cc.hpp" />
    <ClInclude Include="bind\bind_mf2_cc.hpp" />
    <ClInclude Include="bind\bind_mf_cc.hpp" />
    <ClInclude Include="bind\bind_template.hpp" />
    <ClInclude Include="bind\mem_fn.hpp" />
    <ClInclude Include="bind\mem_fn_cc.hpp" />
    <ClInclude Include="bind\mem_fn_template.hpp" />
    <ClInclude Include="bind\mem_fn_vw.hpp" />
    <ClInclude Include="bind\placeholders.hpp" />
    <ClInclude Include="bind\storage.hpp" />
    <ClInclude Include="blank.hpp" />
    <ClInclude Include="blank_fwd.hpp" />
    <ClInclude Include="call_traits.hpp" />
    <ClInclude Include="cerrno.hpp" />
    <ClInclude Include="checked_delete.hpp" />
    <ClInclude Include="chrono\ceil.hpp" />
    <ClInclude Include="chrono\chrono.hpp" />
    <ClInclude Include="chrono\clock_string.hpp" />
    <ClInclude Include="chrono\config.hpp" />
    <ClInclude Include="chrono\detail\inlined\chrono.hpp" />
    <ClInclude Include="chrono\detail\inlined\mac\chrono.hpp" />
    <ClInclude Include="chrono\detail\inlined\mac\process_cpu_clocks.hpp" />
    <ClInclude Include="chrono\detail\inlined\posix\chrono.hpp" />
    <ClInclude Include="chrono\detail\inlined\posix\process_cpu_clocks.hpp" />
    <ClInclude Include="chrono\detail\inlined\process_cpu_clocks.hpp" />
    <ClInclude Include="chrono\detail\inlined\thread_clock.hpp" />
    <ClInclude Include="chrono\detail\inlined\win\chrono.hpp" />
    <ClInclude Include="chrono\detail\inlined\win\process_cpu_clocks.hpp" />
    <ClInclude Include="chrono\detail\inlined\win\thread_clock.hpp" />
    <ClInclude Include="chrono\detail\is_evenly_divisible_by.hpp" />
    <ClInclude Include="chrono\detail\static_assert.hpp" />
    <ClInclude Include="chrono\detail\system.hpp" />
    <ClInclude Include="chrono\duration.hpp" />
    <ClInclude Include="chrono\process_cpu_clocks.hpp" />
    <ClInclude Include="chrono\system_clocks.hpp" />
    <ClInclude Include="chrono\thread_clock.hpp" />
    <ClInclude Include="chrono\time_point.hpp" />
    <ClInclude Include="concept\assert.hpp" />
    <ClInclude Include="concept\detail\backward_compatibility.hpp" />
    <ClInclude Include="concept\detail\borland.hpp" />
    <ClInclude Include="concept\detail\concept_def.hpp" />
    <ClInclude Include="concept\detail\concept_undef.hpp" />
    <ClInclude Include="concept\detail\general.hpp" />
    <ClInclude Include="concept\detail\has_constraints.hpp" />
    <ClInclude Include="concept\detail\msvc.hpp" />
    <ClInclude Include="concept\usage.hpp" />
    <ClInclude Include="concept_check.hpp" />
    <ClInclude Include="config.hpp" />
    <ClInclude Include="config\abi\borland_prefix.hpp" />
    <ClInclude Include="config\abi\borland_suffix.hpp" />
    <ClInclude Include="config\abi\msvc_prefix.hpp" />
    <ClInclude Include="config\abi\msvc_suffix.hpp" />
    <ClInclude Include="config\abi_prefix.hpp" />
    <ClInclude Include="config\abi_suffix.hpp" />
    <ClInclude Include="config\auto_link.hpp" />
    <ClInclude Include="config\compiler\borland.hpp" />
    <ClInclude Include="config\compiler\clang.hpp" />
    <ClInclude Include="config\compiler\codegear.hpp" />
    <ClInclude Include="config\compiler\comeau.hpp" />
    <ClInclude Include="config\compiler\common_edg.hpp" />
    <ClInclude Include="config\compiler\compaq_cxx.hpp" />
    <ClInclude Include="config\compiler\cray.hpp" />
    <ClInclude Include="config\compiler\digitalmars.hpp" />
    <ClInclude Include="config\compiler\gcc.hpp" />
    <ClInclude Include="config\compiler\gcc_xml.hpp" />
    <ClInclude Include="config\compiler\greenhills.hpp" />
    <ClInclude Include="config\compiler\hp_acc.hpp" />
    <ClInclude Include="config\compiler\intel.hpp" />
    <ClInclude Include="config\compiler\kai.hpp" />
    <ClInclude Include="config\compiler\metrowerks.hpp" />
    <ClInclude Include="config\compiler\mpw.hpp" />
    <ClInclude Include="config\compiler\nvcc.hpp" />
    <ClInclude Include="config\compiler\pathscale.hpp" />
    <ClInclude Include="config\compiler\pgi.hpp" />
    <ClInclude Include="config\compiler\sgi_mipspro.hpp" />
    <ClInclude Include="config\compiler\sunpro_cc.hpp" />
    <ClInclude Include="config\compiler\vacpp.hpp" />
    <ClInclude Include="config\compiler\visualc.hpp" />
    <ClInclude Include="config\compiler\xlcpp.hpp" />
    <ClInclude Include="config\no_tr1\cmath.hpp" />
    <ClInclude Include="config\no_tr1\complex.hpp" />
    <ClInclude Include="config\no_tr1\functional.hpp" />
    <ClInclude Include="config\no_tr1\memory.hpp" />
    <ClInclude Include="config\no_tr1\utility.hpp" />
    <ClInclude Include="config\platform\aix.hpp" />
    <ClInclude Include="config\platform\amigaos.hpp" />
    <ClInclude Include="config\platform\beos.hpp" />
    <ClInclude Include="config\platform\bsd.hpp" />
    <ClInclude Include="config\platform\cloudabi.hpp" />
    <ClInclude Include="config\platform\cray.hpp" />
    <ClInclude Include="config\platform\cygwin.hpp" />
    <ClInclude Include="config\platform\haiku.hpp" />
    <ClInclude Include="config\platform\hpux.hpp" />
    <ClInclude Include="config\platform\irix.hpp" />
    <ClInclude Include="config\platform\linux.hpp" />
    <ClInclude Include="config\platform\macos.hpp" />
    <ClInclude Include="config\platform\qnxnto.hpp" />
    <ClInclude Include="config\platform\solaris.hpp" />
    <ClInclude Include="config\platform\symbian.hpp" />
    <ClInclude Include="config\platform\vms.hpp" />
    <ClInclude Include="config\platform\vxworks.hpp" />
    <ClInclude Include="config\platform\win32.hpp" />
    <ClInclude Include="config\posix_features.hpp" />
    <ClInclude Include="config\requires_threads.hpp" />
    <ClInclude Include="config\select_compiler_config.hpp" />
    <ClInclude Include="config\select_platform_config.hpp" />
    <ClInclude Include="config\select_stdlib_config.hpp" />
    <ClInclude Include="config\stdlib\dinkumware.hpp" />
    <ClInclude Include="config\stdlib\libcomo.hpp" />
    <ClInclude Include="config\stdlib\libcpp.hpp" />
    <ClInclude Include="config\stdlib\libstdcpp3.hpp" />
    <ClInclude Include="config\stdlib\modena.hpp" />
    <ClInclude Include="config\stdlib\msl.hpp" />
    <ClInclude Include="config\stdlib\roguewave.hpp" />
    <ClInclude Include="config\stdlib\sgi.hpp" />
    <ClInclude Include="config\stdlib\stlport.hpp" />
    <ClInclude Include="config\stdlib\vacpp.hpp" />
    <ClInclude Include="config\suffix.hpp" />
    <ClInclude Include="config\user.hpp" />
    <ClInclude Include="config\warning_disable.hpp" />
    <ClInclude Include="container\allocator_traits.hpp" />
    <ClInclude Include="container\container_fwd.hpp" />
    <ClInclude Include="container\detail\addressof.hpp" />
    <ClInclude Include="container\detail\advanced_insert_int.hpp" />
    <ClInclude Include="container\detail\algorithm.hpp" />
    <ClInclude Include="container\detail\allocation_type.hpp" />
    <ClInclude Include="container\detail\alloc_helpers.hpp" />
    <ClInclude Include="container\detail\config_begin.hpp" />
    <ClInclude Include="container\detail\config_end.hpp" />
    <ClInclude Include="container\detail\copy_move_algo.hpp" />
    <ClInclude Include="container\detail\destroyers.hpp" />
    <ClInclude Include="container\detail\dispatch_uses_allocator.hpp" />
    <ClInclude Include="container\detail\iterator.hpp" />
    <ClInclude Include="container\detail\iterators.hpp" />
    <ClInclude Include="container\detail\iterator_to_raw_pointer.hpp" />
    <ClInclude Include="container\detail\min_max.hpp" />
    <ClInclude Include="container\detail\mpl.hpp" />
    <ClInclude Include="container\detail\next_capacity.hpp" />
    <ClInclude Include="container\detail\pair.hpp" />
    <ClInclude Include="container\detail\placement_new.hpp" />
    <ClInclude Include="container\detail\std_fwd.hpp" />
    <ClInclude Include="container\detail\to_raw_pointer.hpp" />
    <ClInclude Include="container\detail\type_traits.hpp" />
    <ClInclude Include="container\detail\value_init.hpp" />
    <ClInclude Include="container\detail\variadic_templates_tools.hpp" />
    <ClInclude Include="container\detail\version_type.hpp" />
    <ClInclude Include="container\detail\workaround.hpp" />
    <ClInclude Include="container\new_allocator.hpp" />
    <ClInclude Include="container\scoped_allocator.hpp" />
    <ClInclude Include="container\scoped_allocator_fwd.hpp" />
    <ClInclude Include="container\throw_exception.hpp" />
    <ClInclude Include="container\uses_allocator.hpp" />
    <ClInclude Include="container\uses_allocator_fwd.hpp" />
    <ClInclude Include="container\vector.hpp" />
    <ClInclude Include="core\addressof.hpp" />
    <ClInclude Include="core\checked_delete.hpp" />
    <ClInclude Include="core\demangle.hpp" />
    <ClInclude Include="core\enable_if.hpp" />
    <ClInclude Include="core\explicit_operator_bool.hpp" />
    <ClInclude Include="core\ignore_unused.hpp" />
    <ClInclude Include="core\is_same.hpp" />
    <ClInclude Include="core\noncopyable.hpp" />
    <ClInclude Include="core\no_exceptions_support.hpp" />
    <ClInclude Include="core\ref.hpp" />
    <ClInclude Include="core\scoped_enum.hpp" />
    <ClInclude Include="core\swap.hpp" />
    <ClInclude Include="core\typeinfo.hpp" />
    <ClInclude Include="cstdint.hpp" />
    <ClInclude Include="current_function.hpp" />
    <ClInclude Include="date_time\adjust_functors.hpp" />
    <ClInclude Include="date_time\compiler_config.hpp" />
    <ClInclude Include="date_time\constrained_value.hpp" />
    <ClInclude Include="date_time\c_time.hpp" />
    <ClInclude Include="date_time\date.hpp" />
    <ClInclude Include="date_time\date_clock_device.hpp" />
    <ClInclude Include="date_time\date_defs.hpp" />
    <ClInclude Include="date_time\date_duration.hpp" />
    <ClInclude Include="date_time\date_duration_types.hpp" />
    <ClInclude Include="date_time\date_formatting.hpp" />
    <ClInclude Include="date_time\date_formatting_limited.hpp" />
    <ClInclude Include="date_time\date_formatting_locales.hpp" />
    <ClInclude Include="date_time\date_format_simple.hpp" />
    <ClInclude Include="date_time\date_generators.hpp" />
    <ClInclude Include="date_time\date_iterator.hpp" />
    <ClInclude Include="date_time\date_names_put.hpp" />
    <ClInclude Include="date_time\date_parsing.hpp" />
    <ClInclude Include="date_time\dst_rules.hpp" />
    <ClInclude Include="date_time\filetime_functions.hpp" />
    <ClInclude Include="date_time\gregorian\conversion.hpp" />
    <ClInclude Include="date_time\gregorian\formatters.hpp" />
    <ClInclude Include="date_time\gregorian\formatters_limited.hpp" />
    <ClInclude Include="date_time\gregorian\gregorian_types.hpp" />
    <ClInclude Include="date_time\gregorian\greg_calendar.hpp" />
    <ClInclude Include="date_time\gregorian\greg_date.hpp" />
    <ClInclude Include="date_time\gregorian\greg_day.hpp" />
    <ClInclude Include="date_time\gregorian\greg_day_of_year.hpp" />
    <ClInclude Include="date_time\gregorian\greg_duration.hpp" />
    <ClInclude Include="date_time\gregorian\greg_duration_types.hpp" />
    <ClInclude Include="date_time\gregorian\greg_facet.hpp" />
    <ClInclude Include="date_time\gregorian\greg_month.hpp" />
    <ClInclude Include="date_time\gregorian\greg_weekday.hpp" />
    <ClInclude Include="date_time\gregorian\greg_year.hpp" />
    <ClInclude Include="date_time\gregorian\greg_ymd.hpp" />
    <ClInclude Include="date_time\gregorian\parsers.hpp" />
    <ClInclude Include="date_time\gregorian_calendar.hpp" />
    <ClInclude Include="date_time\int_adapter.hpp" />
    <ClInclude Include="date_time\iso_format.hpp" />
    <ClInclude Include="date_time\locale_config.hpp" />
    <ClInclude Include="date_time\microsec_time_clock.hpp" />
    <ClInclude Include="date_time\parse_format_base.hpp" />
    <ClInclude Include="date_time\period.hpp" />
    <ClInclude Include="date_time\posix_time\conversion.hpp" />
    <ClInclude Include="date_time\posix_time\date_duration_operators.hpp" />
    <ClInclude Include="date_time\posix_time\posix_time_config.hpp" />
    <ClInclude Include="date_time\posix_time\posix_time_duration.hpp" />
    <ClInclude Include="date_time\posix_time\posix_time_system.hpp" />
    <ClInclude Include="date_time\posix_time\posix_time_types.hpp" />
    <ClInclude Include="date_time\posix_time\ptime.hpp" />
    <ClInclude Include="date_time\posix_time\time_period.hpp" />
    <ClInclude Include="date_time\special_defs.hpp" />
    <ClInclude Include="date_time\time.hpp" />
    <ClInclude Include="date_time\time_clock.hpp" />
    <ClInclude Include="date_time\time_defs.hpp" />
    <ClInclude Include="date_time\time_duration.hpp" />
    <ClInclude Include="date_time\time_iterator.hpp" />
    <ClInclude Include="date_time\time_resolution_traits.hpp" />
    <ClInclude Include="date_time\time_system_counted.hpp" />
    <ClInclude Include="date_time\time_system_split.hpp" />
    <ClInclude Include="date_time\wrapping_int.hpp" />
    <ClInclude Include="date_time\year_month_day.hpp" />
    <ClInclude Include="detail\atomic_redef_macros.hpp" />
    <ClInclude Include="detail\atomic_undef_macros.hpp" />
    <ClInclude Include="detail\basic_pointerbuf.hpp" />
    <ClInclude Include="detail\bitmask.hpp" />
    <ClInclude Include="detail\call_traits.hpp" />
    <ClInclude Include="detail\container_fwd.hpp" />
    <ClInclude Include="detail\endian.hpp" />
    <ClInclude Include="detail\fenv.hpp" />
    <ClInclude Include="detail\indirect_traits.hpp" />
    <ClInclude Include="detail\interlocked.hpp" />
    <ClInclude Include="detail\is_xxx.hpp" />
    <ClInclude Include="detail\iterator.hpp" />
    <ClInclude Include="detail\lcast_precision.hpp" />
    <ClInclude Include="detail\lightweight_mutex.hpp" />
    <ClInclude Include="detail\no_exceptions_support.hpp" />
    <ClInclude Include="detail\reference_content.hpp" />
    <ClInclude Include="detail\scoped_enum_emulation.hpp" />
    <ClInclude Include="detail\sp_typeinfo.hpp" />
    <ClInclude Include="detail\templated_streams.hpp" />
    <ClInclude Include="detail\utf8_codecvt_facet.hpp" />
    <ClInclude Include="detail\winapi\basic_types.hpp" />
    <ClInclude Include="detail\winapi\config.hpp" />
    <ClInclude Include="detail\winapi\crypt.hpp" />
    <ClInclude Include="detail\winapi\detail\cast_ptr.hpp" />
    <ClInclude Include="detail\winapi\GetCurrentProcess.hpp" />
    <ClInclude Include="detail\winapi\GetCurrentThread.hpp" />
    <ClInclude Include="detail\winapi\GetLastError.hpp" />
    <ClInclude Include="detail\winapi\GetProcessTimes.hpp" />
    <ClInclude Include="detail\winapi\GetThreadTimes.hpp" />
    <ClInclude Include="detail\winapi\process.hpp" />
    <ClInclude Include="detail\winapi\thread.hpp" />
    <ClInclude Include="detail\winapi\time.hpp" />
    <ClInclude Include="detail\winapi\timers.hpp" />
    <ClInclude Include="detail\workaround.hpp" />
    <ClInclude Include="enable_shared_from_this.hpp" />
    <ClInclude Include="exception\current_exception_cast.hpp" />
    <ClInclude Include="exception\detail\clone_current_exception.hpp" />
    <ClInclude Include="exception\detail\error_info_impl.hpp" />
    <ClInclude Include="exception\detail\exception_ptr.hpp" />
    <ClInclude Include="exception\detail\is_output_streamable.hpp" />
    <ClInclude Include="exception\detail\object_hex_dump.hpp" />
    <ClInclude Include="exception\detail\type_info.hpp" />
    <ClInclude Include="exception\diagnostic_information.hpp" />
    <ClInclude Include="exception\exception.hpp" />
    <ClInclude Include="exception\get_error_info.hpp" />
    <ClInclude Include="exception\info.hpp" />
    <ClInclude Include="exception\to_string.hpp" />
    <ClInclude Include="exception\to_string_stub.hpp" />
    <ClInclude Include="exception_ptr.hpp" />
    <ClInclude Include="filesystem.hpp" />
    <ClInclude Include="filesystem\config.hpp" />
    <ClInclude Include="filesystem\convenience.hpp" />
    <ClInclude Include="filesystem\detail\utf8_codecvt_facet.hpp" />
    <ClInclude Include="filesystem\fstream.hpp" />
    <ClInclude Include="filesystem\operations.hpp" />
    <ClInclude Include="filesystem\path.hpp" />
    <ClInclude Include="filesystem\path_traits.hpp" />
    <ClInclude Include="filesystem\string_file.hpp" />
    <ClInclude Include="function.hpp" />
    <ClInclude Include="functional\hash.hpp" />
    <ClInclude Include="functional\hash\detail\float_functions.hpp" />
    <ClInclude Include="functional\hash\detail\hash_float.hpp" />
    <ClInclude Include="functional\hash\detail\limits.hpp" />
    <ClInclude Include="functional\hash\extensions.hpp" />
    <ClInclude Include="functional\hash\hash.hpp" />
    <ClInclude Include="functional\hash\hash_fwd.hpp" />
    <ClInclude Include="functional\hash_fwd.hpp" />
    <ClInclude Include="function\detail\function_iterate.hpp" />
    <ClInclude Include="function\detail\maybe_include.hpp" />
    <ClInclude Include="function\detail\prologue.hpp" />
    <ClInclude Include="function\function0.hpp" />
    <ClInclude Include="function\function1.hpp" />
    <ClInclude Include="function\function10.hpp" />
    <ClInclude Include="function\function2.hpp" />
    <ClInclude Include="function\function3.hpp" />
    <ClInclude Include="function\function4.hpp" />
    <ClInclude Include="function\function5.hpp" />
    <ClInclude Include="function\function6.hpp" />
    <ClInclude Include="function\function7.hpp" />
    <ClInclude Include="function\function8.hpp" />
    <ClInclude Include="function\function9.hpp" />
    <ClInclude Include="function\function_base.hpp" />
    <ClInclude Include="function\function_fwd.hpp" />
    <ClInclude Include="function\function_template.hpp" />
    <ClInclude Include="function_equal.hpp" />
    <ClInclude Include="function_output_iterator.hpp" />
    <ClInclude Include="get_pointer.hpp" />
    <ClInclude Include="integer.hpp" />
    <ClInclude Include="integer\common_factor_rt.hpp" />
    <ClInclude Include="integer\integer_log2.hpp" />
    <ClInclude Include="integer\integer_mask.hpp" />
    <ClInclude Include="integer\static_log2.hpp" />
    <ClInclude Include="integer_fwd.hpp" />
    <ClInclude Include="integer_traits.hpp" />
    <ClInclude Include="intrusive\detail\algorithm.hpp" />
    <ClInclude Include="intrusive\detail\config_begin.hpp" />
    <ClInclude Include="intrusive\detail\config_end.hpp" />
    <ClInclude Include="intrusive\detail\has_member_function_callable_with.hpp" />
    <ClInclude Include="intrusive\detail\iterator.hpp" />
    <ClInclude Include="intrusive\detail\minimal_pair_header.hpp" />
    <ClInclude Include="intrusive\detail\mpl.hpp" />
    <ClInclude Include="intrusive\detail\pointer_element.hpp" />
    <ClInclude Include="intrusive\detail\reverse_iterator.hpp" />
    <ClInclude Include="intrusive\detail\std_fwd.hpp" />
    <ClInclude Include="intrusive\detail\to_raw_pointer.hpp" />
    <ClInclude Include="intrusive\detail\workaround.hpp" />
    <ClInclude Include="intrusive\pointer_rebind.hpp" />
    <ClInclude Include="intrusive\pointer_traits.hpp" />
    <ClInclude Include="intrusive_ptr.hpp" />
    <ClInclude Include="iostreams\categories.hpp" />
    <ClInclude Include="iostreams\char_traits.hpp" />
    <ClInclude Include="iostreams\checked_operations.hpp" />
    <ClInclude Include="iostreams\close.hpp" />
    <ClInclude Include="iostreams\concepts.hpp" />
    <ClInclude Include="iostreams\constants.hpp" />
    <ClInclude Include="iostreams\detail\adapter\non_blocking_adapter.hpp" />
    <ClInclude Include="iostreams\detail\adapter\range_adapter.hpp" />
    <ClInclude Include="iostreams\detail\bool_trait_def.hpp" />
    <ClInclude Include="iostreams\detail\buffer.hpp" />
    <ClInclude Include="iostreams\detail\char_traits.hpp" />
    <ClInclude Include="iostreams\detail\config\auto_link.hpp" />
    <ClInclude Include="iostreams\detail\config\bzip2.hpp" />
    <ClInclude Include="iostreams\detail\config\codecvt.hpp" />
    <ClInclude Include="iostreams\detail\config\disable_warnings.hpp" />
    <ClInclude Include="iostreams\detail\config\dyn_link.hpp" />
    <ClInclude Include="iostreams\detail\config\enable_warnings.hpp" />
    <ClInclude Include="iostreams\detail\config\fpos.hpp" />
    <ClInclude Include="iostreams\detail\config\limits.hpp" />
    <ClInclude Include="iostreams\detail\config\rtl.hpp" />
    <ClInclude Include="iostreams\detail\config\unreachable_return.hpp" />
    <ClInclude Include="iostreams\detail\config\wide_streams.hpp" />
    <ClInclude Include="iostreams\detail\config\windows_posix.hpp" />
    <ClInclude Include="iostreams\detail\config\zlib.hpp" />
    <ClInclude Include="iostreams\detail\default_arg.hpp" />
    <ClInclude Include="iostreams\detail\dispatch.hpp" />
    <ClInclude Include="iostreams\detail\enable_if_stream.hpp" />
    <ClInclude Include="iostreams\detail\error.hpp" />
    <ClInclude Include="iostreams\detail\file_handle.hpp" />
    <ClInclude Include="iostreams\detail\ios.hpp" />
    <ClInclude Include="iostreams\detail\is_iterator_range.hpp" />
    <ClInclude Include="iostreams\detail\path.hpp" />
    <ClInclude Include="iostreams\detail\select.hpp" />
    <ClInclude Include="iostreams\detail\select_by_size.hpp" />
    <ClInclude Include="iostreams\detail\streambuf.hpp" />
    <ClInclude Include="iostreams\detail\system_failure.hpp" />
    <ClInclude Include="iostreams\detail\template_params.hpp" />
    <ClInclude Include="iostreams\detail\vc6\close.hpp" />
    <ClInclude Include="iostreams\detail\vc6\read.hpp" />
    <ClInclude Include="iostreams\detail\vc6\write.hpp" />
    <ClInclude Include="iostreams\detail\wrap_unwrap.hpp" />
    <ClInclude Include="iostreams\device\back_inserter.hpp" />
    <ClInclude Include="iostreams\device\file_descriptor.hpp" />
    <ClInclude Include="iostreams\device\mapped_file.hpp" />
    <ClInclude Include="iostreams\filter\bzip2.hpp" />
    <ClInclude Include="iostreams\filter\gzip.hpp" />
    <ClInclude Include="iostreams\filter\symmetric.hpp" />
    <ClInclude Include="iostreams\filter\zlib.hpp" />
    <ClInclude Include="iostreams\flush.hpp" />
    <ClInclude Include="iostreams\get.hpp" />
    <ClInclude Include="iostreams\imbue.hpp" />
    <ClInclude Include="iostreams\input_sequence.hpp" />
    <ClInclude Include="iostreams\operations.hpp" />
    <ClInclude Include="iostreams\operations_fwd.hpp" />
    <ClInclude Include="iostreams\optimal_buffer_size.hpp" />
    <ClInclude Include="iostreams\output_sequence.hpp" />
    <ClInclude Include="iostreams\pipeline.hpp" />
    <ClInclude Include="iostreams\positioning.hpp" />
    <ClInclude Include="iostreams\put.hpp" />
    <ClInclude Include="iostreams\putback.hpp" />
    <ClInclude Include="iostreams\read.hpp" />
    <ClInclude Include="iostreams\seek.hpp" />
    <ClInclude Include="iostreams\traits.hpp" />
    <ClInclude Include="iostreams\traits_fwd.hpp" />
    <ClInclude Include="iostreams\write.hpp" />
    <ClInclude Include="io\detail\quoted_manip.hpp" />
    <ClInclude Include="io\ios_state.hpp" />
    <ClInclude Include="io_fwd.hpp" />
    <ClInclude Include="is_placeholder.hpp" />
    <ClInclude Include="iterator.hpp" />
    <ClInclude Include="iterator\detail\config_def.hpp" />
    <ClInclude Include="iterator\detail\config_undef.hpp" />
    <ClInclude Include="iterator\detail\enable_if.hpp" />
    <ClInclude Include="iterator\detail\facade_iterator_category.hpp" />
    <ClInclude Include="iterator\interoperable.hpp" />
    <ClInclude Include="iterator\iterator_adaptor.hpp" />
    <ClInclude Include="iterator\iterator_categories.hpp" />
    <ClInclude Include="iterator\iterator_concepts.hpp" />
    <ClInclude Include="iterator\iterator_facade.hpp" />
    <ClInclude Include="iterator\iterator_traits.hpp" />
    <ClInclude Include="iterator\minimum_category.hpp" />
    <ClInclude Include="iterator\reverse_iterator.hpp" />
    <ClInclude Include="iterator\transform_iterator.hpp" />
    <ClInclude Include="lexical_cast.hpp" />
    <ClInclude Include="lexical_cast\bad_lexical_cast.hpp" />
    <ClInclude Include="lexical_cast\detail\converter_lexical.hpp" />
    <ClInclude Include="lexical_cast\detail\converter_lexical_streams.hpp" />
    <ClInclude Include="lexical_cast\detail\converter_numeric.hpp" />
    <ClInclude Include="lexical_cast\detail\inf_nan.hpp" />
    <ClInclude Include="lexical_cast\detail\is_character.hpp" />
    <ClInclude Include="lexical_cast\detail\lcast_char_constants.hpp" />
    <ClInclude Include="lexical_cast\detail\lcast_unsigned_converters.hpp" />
    <ClInclude Include="lexical_cast\detail\widest_char.hpp" />
    <ClInclude Include="lexical_cast\try_lexical_convert.hpp" />
    <ClInclude Include="libs\date_time\src\gregorian\greg_names.hpp" />
    <ClInclude Include="libs\filesystem\src\windows_file_codecvt.hpp" />
    <ClInclude Include="limits.hpp" />
    <ClInclude Include="make_shared.hpp" />
    <ClInclude Include="math\common_factor_ct.hpp" />
    <ClInclude Include="math\policies\policy.hpp" />
    <ClInclude Include="math\special_functions\detail\fp_traits.hpp" />
    <ClInclude Include="math\special_functions\detail\round_fwd.hpp" />
    <ClInclude Include="math\special_functions\fpclassify.hpp" />
    <ClInclude Include="math\special_functions\math_fwd.hpp" />
    <ClInclude Include="math\special_functions\sign.hpp" />
    <ClInclude Include="math\tools\config.hpp" />
    <ClInclude Include="math\tools\promotion.hpp" />
    <ClInclude Include="math\tools\real_cast.hpp" />
    <ClInclude Include="math\tools\user.hpp" />
    <ClInclude Include="math_fwd.hpp" />
    <ClInclude Include="memory_order.hpp" />
    <ClInclude Include="mem_fn.hpp" />
    <ClInclude Include="move\adl_move_swap.hpp" />
    <ClInclude Include="move\algorithm.hpp" />
    <ClInclude Include="move\core.hpp" />
    <ClInclude Include="move\default_delete.hpp" />
    <ClInclude Include="move\detail\config_begin.hpp" />
    <ClInclude Include="move\detail\config_end.hpp" />
    <ClInclude Include="move\detail\fwd_macros.hpp" />
    <ClInclude Include="move\detail\iterator_traits.hpp" />
    <ClInclude Include="move\detail\meta_utils.hpp" />
    <ClInclude Include="move\detail\meta_utils_core.hpp" />
    <ClInclude Include="move\detail\move_helpers.hpp" />
    <ClInclude Include="move\detail\std_ns_begin.hpp" />
    <ClInclude Include="move\detail\std_ns_end.hpp" />
    <ClInclude Include="move\detail\type_traits.hpp" />
    <ClInclude Include="move\detail\unique_ptr_meta_utils.hpp" />
    <ClInclude Include="move\detail\workaround.hpp" />
    <ClInclude Include="move\iterator.hpp" />
    <ClInclude Include="move\make_unique.hpp" />
    <ClInclude Include="move\move.hpp" />
    <ClInclude Include="move\traits.hpp" />
    <ClInclude Include="move\unique_ptr.hpp" />
    <ClInclude Include="move\utility.hpp" />
    <ClInclude Include="move\utility_core.hpp" />
    <ClInclude Include="mpl\advance.hpp" />
    <ClInclude Include="mpl\advance_fwd.hpp" />
    <ClInclude Include="mpl\always.hpp" />
    <ClInclude Include="mpl\and.hpp" />
    <ClInclude Include="mpl\apply.hpp" />
    <ClInclude Include="mpl\apply_fwd.hpp" />
    <ClInclude Include="mpl\apply_wrap.hpp" />
    <ClInclude Include="mpl\arg.hpp" />
    <ClInclude Include="mpl\arg_fwd.hpp" />
    <ClInclude Include="mpl\assert.hpp" />
    <ClInclude Include="mpl\at.hpp" />
    <ClInclude Include="mpl\at_fwd.hpp" />
    <ClInclude Include="mpl\aux_\adl_barrier.hpp" />
    <ClInclude Include="mpl\aux_\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\arg_typedef.hpp" />
    <ClInclude Include="mpl\aux_\arithmetic_op.hpp" />
    <ClInclude Include="mpl\aux_\arity.hpp" />
    <ClInclude Include="mpl\aux_\arity_spec.hpp" />
    <ClInclude Include="mpl\aux_\at_impl.hpp" />
    <ClInclude Include="mpl\aux_\begin_end_impl.hpp" />
    <ClInclude Include="mpl\aux_\clear_impl.hpp" />
    <ClInclude Include="mpl\aux_\common_name_wknd.hpp" />
    <ClInclude Include="mpl\aux_\comparison_op.hpp" />
    <ClInclude Include="mpl\aux_\config\adl.hpp" />
    <ClInclude Include="mpl\aux_\config\arrays.hpp" />
    <ClInclude Include="mpl\aux_\config\bcc.hpp" />
    <ClInclude Include="mpl\aux_\config\bind.hpp" />
    <ClInclude Include="mpl\aux_\config\compiler.hpp" />
    <ClInclude Include="mpl\aux_\config\ctps.hpp" />
    <ClInclude Include="mpl\aux_\config\dependent_nttp.hpp" />
    <ClInclude Include="mpl\aux_\config\dmc_ambiguous_ctps.hpp" />
    <ClInclude Include="mpl\aux_\config\dtp.hpp" />
    <ClInclude Include="mpl\aux_\config\eti.hpp" />
    <ClInclude Include="mpl\aux_\config\forwarding.hpp" />
    <ClInclude Include="mpl\aux_\config\gcc.hpp" />
    <ClInclude Include="mpl\aux_\config\gpu.hpp" />
    <ClInclude Include="mpl\aux_\config\has_apply.hpp" />
    <ClInclude Include="mpl\aux_\config\has_xxx.hpp" />
    <ClInclude Include="mpl\aux_\config\integral.hpp" />
    <ClInclude Include="mpl\aux_\config\intel.hpp" />
    <ClInclude Include="mpl\aux_\config\lambda.hpp" />
    <ClInclude Include="mpl\aux_\config\msvc.hpp" />
    <ClInclude Include="mpl\aux_\config\msvc_typename.hpp" />
    <ClInclude Include="mpl\aux_\config\nttp.hpp" />
    <ClInclude Include="mpl\aux_\config\operators.hpp" />
    <ClInclude Include="mpl\aux_\config\overload_resolution.hpp" />
    <ClInclude Include="mpl\aux_\config\pp_counter.hpp" />
    <ClInclude Include="mpl\aux_\config\preprocessor.hpp" />
    <ClInclude Include="mpl\aux_\config\static_constant.hpp" />
    <ClInclude Include="mpl\aux_\config\ttp.hpp" />
    <ClInclude Include="mpl\aux_\config\typeof.hpp" />
    <ClInclude Include="mpl\aux_\config\use_preprocessed.hpp" />
    <ClInclude Include="mpl\aux_\config\workaround.hpp" />
    <ClInclude Include="mpl\aux_\contains_impl.hpp" />
    <ClInclude Include="mpl\aux_\count_args.hpp" />
    <ClInclude Include="mpl\aux_\empty_impl.hpp" />
    <ClInclude Include="mpl\aux_\find_if_pred.hpp" />
    <ClInclude Include="mpl\aux_\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\fold_impl_body.hpp" />
    <ClInclude Include="mpl\aux_\front_impl.hpp" />
    <ClInclude Include="mpl\aux_\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\has_apply.hpp" />
    <ClInclude Include="mpl\aux_\has_begin.hpp" />
    <ClInclude Include="mpl\aux_\has_key_impl.hpp" />
    <ClInclude Include="mpl\aux_\has_rebind.hpp" />
    <ClInclude Include="mpl\aux_\has_size.hpp" />
    <ClInclude Include="mpl\aux_\has_tag.hpp" />
    <ClInclude Include="mpl\aux_\has_type.hpp" />
    <ClInclude Include="mpl\aux_\include_preprocessed.hpp" />
    <ClInclude Include="mpl\aux_\inserter_algorithm.hpp" />
    <ClInclude Include="mpl\aux_\insert_impl.hpp" />
    <ClInclude Include="mpl\aux_\integral_wrapper.hpp" />
    <ClInclude Include="mpl\aux_\is_msvc_eti_arg.hpp" />
    <ClInclude Include="mpl\aux_\iter_apply.hpp" />
    <ClInclude Include="mpl\aux_\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\lambda_arity_param.hpp" />
    <ClInclude Include="mpl\aux_\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\lambda_spec.hpp" />
    <ClInclude Include="mpl\aux_\lambda_support.hpp" />
    <ClInclude Include="mpl\aux_\largest_int.hpp" />
    <ClInclude Include="mpl\aux_\logical_op.hpp" />
    <ClInclude Include="mpl\aux_\msvc_dtw.hpp" />
    <ClInclude Include="mpl\aux_\msvc_eti_base.hpp" />
    <ClInclude Include="mpl\aux_\msvc_is_class.hpp" />
    <ClInclude Include="mpl\aux_\msvc_never_true.hpp" />
    <ClInclude Include="mpl\aux_\msvc_type.hpp" />
    <ClInclude Include="mpl\aux_\na.hpp" />
    <ClInclude Include="mpl\aux_\na_assert.hpp" />
    <ClInclude Include="mpl\aux_\na_fwd.hpp" />
    <ClInclude Include="mpl\aux_\na_spec.hpp" />
    <ClInclude Include="mpl\aux_\nested_type_wknd.hpp" />
    <ClInclude Include="mpl\aux_\nttp_decl.hpp" />
    <ClInclude Include="mpl\aux_\numeric_cast_utils.hpp" />
    <ClInclude Include="mpl\aux_\numeric_op.hpp" />
    <ClInclude Include="mpl\aux_\O1_size_impl.hpp" />
    <ClInclude Include="mpl\aux_\overload_names.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\dmc\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\gcc\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\advance_backward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\advance_forward.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\and.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply_wrap.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\arg.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\basic_bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\bind.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\bind_fwd.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitand.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitxor.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\deque.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\divides.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\full_lambda.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\greater.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\greater_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\inherit.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\iter_fold_if_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\lambda_no_ctps.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\less.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\less_equal.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\list.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\list_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\map.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\minus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\modulus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\not_equal_to.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\or.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\placeholders.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\plus.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\quote.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\reverse_iter_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\set.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\set_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\shift_left.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\shift_right.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\times.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\unpack_args.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\vector.hpp" />
    <ClInclude Include="mpl\aux_\preprocessed\plain\vector_c.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\add.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\default_params.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\def_params_tail.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\enum.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\ext_params.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\filter_params.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\params.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\partial_spec_params.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\range.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\repeat.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\sub.hpp" />
    <ClInclude Include="mpl\aux_\preprocessor\tuple.hpp" />
    <ClInclude Include="mpl\aux_\ptr_to_ref.hpp" />
    <ClInclude Include="mpl\aux_\push_back_impl.hpp" />
    <ClInclude Include="mpl\aux_\push_front_impl.hpp" />
    <ClInclude Include="mpl\aux_\reverse_fold_impl.hpp" />
    <ClInclude Include="mpl\aux_\reverse_fold_impl_body.hpp" />
    <ClInclude Include="mpl\aux_\sequence_wrapper.hpp" />
    <ClInclude Include="mpl\aux_\size_impl.hpp" />
    <ClInclude Include="mpl\aux_\static_cast.hpp" />
    <ClInclude Include="mpl\aux_\template_arity.hpp" />
    <ClInclude Include="mpl\aux_\template_arity_fwd.hpp" />
    <ClInclude Include="mpl\aux_\traits_lambda_spec.hpp" />
    <ClInclude Include="mpl\aux_\type_wrapper.hpp" />
    <ClInclude Include="mpl\aux_\value_wknd.hpp" />
    <ClInclude Include="mpl\aux_\yes_no.hpp" />
    <ClInclude Include="mpl\back_fwd.hpp" />
    <ClInclude Include="mpl\back_inserter.hpp" />
    <ClInclude Include="mpl\base.hpp" />
    <ClInclude Include="mpl\begin.hpp" />
    <ClInclude Include="mpl\begin_end.hpp" />
    <ClInclude Include="mpl\begin_end_fwd.hpp" />
    <ClInclude Include="mpl\bind.hpp" />
    <ClInclude Include="mpl\bind_fwd.hpp" />
    <ClInclude Include="mpl\bool.hpp" />
    <ClInclude Include="mpl\bool_fwd.hpp" />
    <ClInclude Include="mpl\clear.hpp" />
    <ClInclude Include="mpl\clear_fwd.hpp" />
    <ClInclude Include="mpl\comparison.hpp" />
    <ClInclude Include="mpl\contains.hpp" />
    <ClInclude Include="mpl\contains_fwd.hpp" />
    <ClInclude Include="mpl\deref.hpp" />
    <ClInclude Include="mpl\distance.hpp" />
    <ClInclude Include="mpl\distance_fwd.hpp" />
    <ClInclude Include="mpl\empty.hpp" />
    <ClInclude Include="mpl\empty_fwd.hpp" />
    <ClInclude Include="mpl\end.hpp" />
    <ClInclude Include="mpl\equal_to.hpp" />
    <ClInclude Include="mpl\erase_fwd.hpp" />
    <ClInclude Include="mpl\erase_key_fwd.hpp" />
    <ClInclude Include="mpl\eval_if.hpp" />
    <ClInclude Include="mpl\find.hpp" />
    <ClInclude Include="mpl\find_if.hpp" />
    <ClInclude Include="mpl\fold.hpp" />
    <ClInclude Include="mpl\front.hpp" />
    <ClInclude Include="mpl\front_fwd.hpp" />
    <ClInclude Include="mpl\front_inserter.hpp" />
    <ClInclude Include="mpl\greater.hpp" />
    <ClInclude Include="mpl\greater_equal.hpp" />
    <ClInclude Include="mpl\has_key.hpp" />
    <ClInclude Include="mpl\has_key_fwd.hpp" />
    <ClInclude Include="mpl\has_xxx.hpp" />
    <ClInclude Include="mpl\identity.hpp" />
    <ClInclude Include="mpl\if.hpp" />
    <ClInclude Include="mpl\insert.hpp" />
    <ClInclude Include="mpl\inserter.hpp" />
    <ClInclude Include="mpl\insert_fwd.hpp" />
    <ClInclude Include="mpl\insert_range_fwd.hpp" />
    <ClInclude Include="mpl\int.hpp" />
    <ClInclude Include="mpl\integral_c.hpp" />
    <ClInclude Include="mpl\integral_c_fwd.hpp" />
    <ClInclude Include="mpl\integral_c_tag.hpp" />
    <ClInclude Include="mpl\int_fwd.hpp" />
    <ClInclude Include="mpl\is_placeholder.hpp" />
    <ClInclude Include="mpl\is_sequence.hpp" />
    <ClInclude Include="mpl\iterator_category.hpp" />
    <ClInclude Include="mpl\iterator_range.hpp" />
    <ClInclude Include="mpl\iterator_tags.hpp" />
    <ClInclude Include="mpl\iter_fold.hpp" />
    <ClInclude Include="mpl\iter_fold_if.hpp" />
    <ClInclude Include="mpl\key_type_fwd.hpp" />
    <ClInclude Include="mpl\lambda.hpp" />
    <ClInclude Include="mpl\lambda_fwd.hpp" />
    <ClInclude Include="mpl\less.hpp" />
    <ClInclude Include="mpl\less_equal.hpp" />
    <ClInclude Include="mpl\limits\arity.hpp" />
    <ClInclude Include="mpl\limits\list.hpp" />
    <ClInclude Include="mpl\limits\unrolling.hpp" />
    <ClInclude Include="mpl\limits\vector.hpp" />
    <ClInclude Include="mpl\list.hpp" />
    <ClInclude Include="mpl\list\aux_\begin_end.hpp" />
    <ClInclude Include="mpl\list\aux_\clear.hpp" />
    <ClInclude Include="mpl\list\aux_\empty.hpp" />
    <ClInclude Include="mpl\list\aux_\front.hpp" />
    <ClInclude Include="mpl\list\aux_\include_preprocessed.hpp" />
    <ClInclude Include="mpl\list\aux_\item.hpp" />
    <ClInclude Include="mpl\list\aux_\iterator.hpp" />
    <ClInclude Include="mpl\list\aux_\numbered.hpp" />
    <ClInclude Include="mpl\list\aux_\numbered_c.hpp" />
    <ClInclude Include="mpl\list\aux_\O1_size.hpp" />
    <ClInclude Include="mpl\list\aux_\pop_front.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list10.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list10_c.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list20.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list20_c.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list30.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list30_c.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list40.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list40_c.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list50.hpp" />
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list50_c.hpp" />
    <ClInclude Include="mpl\list\aux_\push_back.hpp" />
    <ClInclude Include="mpl\list\aux_\push_front.hpp" />
    <ClInclude Include="mpl\list\aux_\size.hpp" />
    <ClInclude Include="mpl\list\aux_\tag.hpp" />
    <ClInclude Include="mpl\list\list0.hpp" />
    <ClInclude Include="mpl\list\list0_c.hpp" />
    <ClInclude Include="mpl\list\list10.hpp" />
    <ClInclude Include="mpl\list\list10_c.hpp" />
    <ClInclude Include="mpl\list\list20.hpp" />
    <ClInclude Include="mpl\list\list20_c.hpp" />
    <ClInclude Include="mpl\list\list30.hpp" />
    <ClInclude Include="mpl\list\list30_c.hpp" />
    <ClInclude Include="mpl\list\list40.hpp" />
    <ClInclude Include="mpl\list\list40_c.hpp" />
    <ClInclude Include="mpl\list\list50.hpp" />
    <ClInclude Include="mpl\list\list50_c.hpp" />
    <ClInclude Include="mpl\logical.hpp" />
    <ClInclude Include="mpl\long.hpp" />
    <ClInclude Include="mpl\long_fwd.hpp" />
    <ClInclude Include="mpl\max_element.hpp" />
    <ClInclude Include="mpl\minus.hpp" />
    <ClInclude Include="mpl\min_max.hpp" />
    <ClInclude Include="mpl\multiplies.hpp" />
    <ClInclude Include="mpl\negate.hpp" />
    <ClInclude Include="mpl\next.hpp" />
    <ClInclude Include="mpl\next_prior.hpp" />
    <ClInclude Include="mpl\not.hpp" />
    <ClInclude Include="mpl\not_equal_to.hpp" />
    <ClInclude Include="mpl\numeric_cast.hpp" />
    <ClInclude Include="mpl\O1_size.hpp" />
    <ClInclude Include="mpl\O1_size_fwd.hpp" />
    <ClInclude Include="mpl\or.hpp" />
    <ClInclude Include="mpl\pair.hpp" />
    <ClInclude Include="mpl\pair_view.hpp" />
    <ClInclude Include="mpl\placeholders.hpp" />
    <ClInclude Include="mpl\plus.hpp" />
    <ClInclude Include="mpl\pop_back_fwd.hpp" />
    <ClInclude Include="mpl\pop_front_fwd.hpp" />
    <ClInclude Include="mpl\prior.hpp" />
    <ClInclude Include="mpl\protect.hpp" />
    <ClInclude Include="mpl\push_back.hpp" />
    <ClInclude Include="mpl\push_back_fwd.hpp" />
    <ClInclude Include="mpl\push_front.hpp" />
    <ClInclude Include="mpl\push_front_fwd.hpp" />
    <ClInclude Include="mpl\quote.hpp" />
    <ClInclude Include="mpl\remove_if.hpp" />
    <ClInclude Include="mpl\reverse_fold.hpp" />
    <ClInclude Include="mpl\same_as.hpp" />
    <ClInclude Include="mpl\sequence_tag.hpp" />
    <ClInclude Include="mpl\sequence_tag_fwd.hpp" />
    <ClInclude Include="mpl\set\aux_\at_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\begin_end_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\clear_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\empty_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\erase_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\erase_key_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\has_key_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\insert_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\insert_range_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\item.hpp" />
    <ClInclude Include="mpl\set\aux_\iterator.hpp" />
    <ClInclude Include="mpl\set\aux_\key_type_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\set0.hpp" />
    <ClInclude Include="mpl\set\aux_\size_impl.hpp" />
    <ClInclude Include="mpl\set\aux_\tag.hpp" />
    <ClInclude Include="mpl\set\aux_\value_type_impl.hpp" />
    <ClInclude Include="mpl\set\set0.hpp" />
    <ClInclude Include="mpl\size.hpp" />
    <ClInclude Include="mpl\sizeof.hpp" />
    <ClInclude Include="mpl\size_fwd.hpp" />
    <ClInclude Include="mpl\size_t.hpp" />
    <ClInclude Include="mpl\size_t_fwd.hpp" />
    <ClInclude Include="mpl\tag.hpp" />
    <ClInclude Include="mpl\times.hpp" />
    <ClInclude Include="mpl\transform.hpp" />
    <ClInclude Include="mpl\value_type_fwd.hpp" />
    <ClInclude Include="mpl\vector.hpp" />
    <ClInclude Include="mpl\vector\aux_\at.hpp" />
    <ClInclude Include="mpl\vector\aux_\back.hpp" />
    <ClInclude Include="mpl\vector\aux_\begin_end.hpp" />
    <ClInclude Include="mpl\vector\aux_\clear.hpp" />
    <ClInclude Include="mpl\vector\aux_\empty.hpp" />
    <ClInclude Include="mpl\vector\aux_\front.hpp" />
    <ClInclude Include="mpl\vector\aux_\include_preprocessed.hpp" />
    <ClInclude Include="mpl\vector\aux_\item.hpp" />
    <ClInclude Include="mpl\vector\aux_\iterator.hpp" />
    <ClInclude Include="mpl\vector\aux_\numbered.hpp" />
    <ClInclude Include="mpl\vector\aux_\numbered_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\O1_size.hpp" />
    <ClInclude Include="mpl\vector\aux_\pop_back.hpp" />
    <ClInclude Include="mpl\vector\aux_\pop_front.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector10.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector10_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector20.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector20_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector30.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector30_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector40.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector40_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector50.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector50_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector10.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector10_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector20.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector20_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector30.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector30_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector40.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector40_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector50.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector50_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector10.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector10_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector20.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector20_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector30.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector30_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector40.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector40_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector50.hpp" />
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector50_c.hpp" />
    <ClInclude Include="mpl\vector\aux_\push_back.hpp" />
    <ClInclude Include="mpl\vector\aux_\push_front.hpp" />
    <ClInclude Include="mpl\vector\aux_\size.hpp" />
    <ClInclude Include="mpl\vector\aux_\tag.hpp" />
    <ClInclude Include="mpl\vector\aux_\vector0.hpp" />
    <ClInclude Include="mpl\vector\vector0.hpp" />
    <ClInclude Include="mpl\vector\vector0_c.hpp" />
    <ClInclude Include="mpl\vector\vector10.hpp" />
    <ClInclude Include="mpl\vector\vector10_c.hpp" />
    <ClInclude Include="mpl\vector\vector20.hpp" />
    <ClInclude Include="mpl\vector\vector20_c.hpp" />
    <ClInclude Include="mpl\vector\vector30.hpp" />
    <ClInclude Include="mpl\vector\vector30_c.hpp" />
    <ClInclude Include="mpl\vector\vector40.hpp" />
    <ClInclude Include="mpl\vector\vector40_c.hpp" />
    <ClInclude Include="mpl\vector\vector50.hpp" />
    <ClInclude Include="mpl\vector\vector50_c.hpp" />
    <ClInclude Include="mpl\void.hpp" />
    <ClInclude Include="mpl\void_fwd.hpp" />
    <ClInclude Include="multi_index\detail\scope_guard.hpp" />
    <ClInclude Include="next_prior.hpp" />
    <ClInclude Include="noncopyable.hpp" />
    <ClInclude Include="none.hpp" />
    <ClInclude Include="none_t.hpp" />
    <ClInclude Include="non_type.hpp" />
    <ClInclude Include="numeric\conversion\bounds.hpp" />
    <ClInclude Include="numeric\conversion\cast.hpp" />
    <ClInclude Include="numeric\conversion\conversion_traits.hpp" />
    <ClInclude Include="numeric\conversion\converter.hpp" />
    <ClInclude Include="numeric\conversion\converter_policies.hpp" />
    <ClInclude Include="numeric\conversion\detail\bounds.hpp" />
    <ClInclude Include="numeric\conversion\detail\conversion_traits.hpp" />
    <ClInclude Include="numeric\conversion\detail\converter.hpp" />
    <ClInclude Include="numeric\conversion\detail\int_float_mixture.hpp" />
    <ClInclude Include="numeric\conversion\detail\is_subranged.hpp" />
    <ClInclude Include="numeric\conversion\detail\meta.hpp" />
    <ClInclude Include="numeric\conversion\detail\numeric_cast_traits.hpp" />
    <ClInclude Include="numeric\conversion\detail\old_numeric_cast.hpp" />
    <ClInclude Include="numeric\conversion\detail\preprocessed\numeric_cast_traits_common.hpp" />
    <ClInclude Include="numeric\conversion\detail\preprocessed\numeric_cast_traits_long_long.hpp" />
    <ClInclude Include="numeric\conversion\detail\sign_mixture.hpp" />
    <ClInclude Include="numeric\conversion\detail\udt_builtin_mixture.hpp" />
    <ClInclude Include="numeric\conversion\int_float_mixture_enum.hpp" />
    <ClInclude Include="numeric\conversion\numeric_cast_traits.hpp" />
    <ClInclude Include="numeric\conversion\sign_mixture_enum.hpp" />
    <ClInclude Include="numeric\conversion\udt_builtin_mixture_enum.hpp" />
    <ClInclude Include="operators.hpp" />
    <ClInclude Include="optional.hpp" />
    <ClInclude Include="optional\bad_optional_access.hpp" />
    <ClInclude Include="optional\optional.hpp" />
    <ClInclude Include="optional\optional_fwd.hpp" />
    <ClInclude Include="parameter.hpp" />
    <ClInclude Include="parameter\aux_\arg_list.hpp" />
    <ClInclude Include="parameter\aux_\cast.hpp" />
    <ClInclude Include="parameter\aux_\default.hpp" />
    <ClInclude Include="parameter\aux_\is_maybe.hpp" />
    <ClInclude Include="parameter\aux_\overloads.hpp" />
    <ClInclude Include="parameter\aux_\parameter_requirements.hpp" />
    <ClInclude Include="parameter\aux_\parenthesized_type.hpp" />
    <ClInclude Include="parameter\aux_\preprocessor\flatten.hpp" />
    <ClInclude Include="parameter\aux_\preprocessor\for_each.hpp" />
    <ClInclude Include="parameter\aux_\result_of0.hpp" />
    <ClInclude Include="parameter\aux_\set.hpp" />
    <ClInclude Include="parameter\aux_\tag.hpp" />
    <ClInclude Include="parameter\aux_\tagged_argument.hpp" />
    <ClInclude Include="parameter\aux_\template_keyword.hpp" />
    <ClInclude Include="parameter\aux_\unwrap_cv_reference.hpp" />
    <ClInclude Include="parameter\aux_\void.hpp" />
    <ClInclude Include="parameter\aux_\yesno.hpp" />
    <ClInclude Include="parameter\binding.hpp" />
    <ClInclude Include="parameter\config.hpp" />
    <ClInclude Include="parameter\keyword.hpp" />
    <ClInclude Include="parameter\macros.hpp" />
    <ClInclude Include="parameter\match.hpp" />
    <ClInclude Include="parameter\name.hpp" />
    <ClInclude Include="parameter\parameters.hpp" />
    <ClInclude Include="parameter\preprocessor.hpp" />
    <ClInclude Include="parameter\value_type.hpp" />
    <ClInclude Include="pending\integer_log2.hpp" />
    <ClInclude Include="predef.h" />
    <ClInclude Include="predef\architecture.h" />
    <ClInclude Include="predef\architecture\alpha.h" />
    <ClInclude Include="predef\architecture\arm.h" />
    <ClInclude Include="predef\architecture\blackfin.h" />
    <ClInclude Include="predef\architecture\convex.h" />
    <ClInclude Include="predef\architecture\ia64.h" />
    <ClInclude Include="predef\architecture\m68k.h" />
    <ClInclude Include="predef\architecture\mips.h" />
    <ClInclude Include="predef\architecture\parisc.h" />
    <ClInclude Include="predef\architecture\ppc.h" />
    <ClInclude Include="predef\architecture\pyramid.h" />
    <ClInclude Include="predef\architecture\rs6k.h" />
    <ClInclude Include="predef\architecture\sparc.h" />
    <ClInclude Include="predef\architecture\superh.h" />
    <ClInclude Include="predef\architecture\sys370.h" />
    <ClInclude Include="predef\architecture\sys390.h" />
    <ClInclude Include="predef\architecture\x86.h" />
    <ClInclude Include="predef\architecture\x86\32.h" />
    <ClInclude Include="predef\architecture\x86\64.h" />
    <ClInclude Include="predef\architecture\z.h" />
    <ClInclude Include="predef\compiler.h" />
    <ClInclude Include="predef\compiler\borland.h" />
    <ClInclude Include="predef\compiler\clang.h" />
    <ClInclude Include="predef\compiler\comeau.h" />
    <ClInclude Include="predef\compiler\compaq.h" />
    <ClInclude Include="predef\compiler\diab.h" />
    <ClInclude Include="predef\compiler\digitalmars.h" />
    <ClInclude Include="predef\compiler\dignus.h" />
    <ClInclude Include="predef\compiler\edg.h" />
    <ClInclude Include="predef\compiler\ekopath.h" />
    <ClInclude Include="predef\compiler\gcc.h" />
    <ClInclude Include="predef\compiler\gcc_xml.h" />
    <ClInclude Include="predef\compiler\greenhills.h" />
    <ClInclude Include="predef\compiler\hp_acc.h" />
    <ClInclude Include="predef\compiler\iar.h" />
    <ClInclude Include="predef\compiler\ibm.h" />
    <ClInclude Include="predef\compiler\intel.h" />
    <ClInclude Include="predef\compiler\kai.h" />
    <ClInclude Include="predef\compiler\llvm.h" />
    <ClInclude Include="predef\compiler\metaware.h" />
    <ClInclude Include="predef\compiler\metrowerks.h" />
    <ClInclude Include="predef\compiler\microtec.h" />
    <ClInclude Include="predef\compiler\mpw.h" />
    <ClInclude Include="predef\compiler\palm.h" />
    <ClInclude Include="predef\compiler\pgi.h" />
    <ClInclude Include="predef\compiler\sgi_mipspro.h" />
    <ClInclude Include="predef\compiler\sunpro.h" />
    <ClInclude Include="predef\compiler\tendra.h" />
    <ClInclude Include="predef\compiler\visualc.h" />
    <ClInclude Include="predef\compiler\watcom.h" />
    <ClInclude Include="predef\detail\comp_detected.h" />
    <ClInclude Include="predef\detail\endian_compat.h" />
    <ClInclude Include="predef\detail\os_detected.h" />
    <ClInclude Include="predef\detail\platform_detected.h" />
    <ClInclude Include="predef\detail\test.h" />
    <ClInclude Include="predef\detail\_cassert.h" />
    <ClInclude Include="predef\detail\_exception.h" />
    <ClInclude Include="predef\hardware.h" />
    <ClInclude Include="predef\hardware\simd.h" />
    <ClInclude Include="predef\hardware\simd\arm.h" />
    <ClInclude Include="predef\hardware\simd\arm\versions.h" />
    <ClInclude Include="predef\hardware\simd\ppc.h" />
    <ClInclude Include="predef\hardware\simd\ppc\versions.h" />
    <ClInclude Include="predef\hardware\simd\x86.h" />
    <ClInclude Include="predef\hardware\simd\x86\versions.h" />
    <ClInclude Include="predef\hardware\simd\x86_amd.h" />
    <ClInclude Include="predef\hardware\simd\x86_amd\versions.h" />
    <ClInclude Include="predef\language.h" />
    <ClInclude Include="predef\language\objc.h" />
    <ClInclude Include="predef\language\stdc.h" />
    <ClInclude Include="predef\language\stdcpp.h" />
    <ClInclude Include="predef\library.h" />
    <ClInclude Include="predef\library\c.h" />
    <ClInclude Include="predef\library\c\gnu.h" />
    <ClInclude Include="predef\library\c\uc.h" />
    <ClInclude Include="predef\library\c\vms.h" />
    <ClInclude Include="predef\library\c\zos.h" />
    <ClInclude Include="predef\library\c\_prefix.h" />
    <ClInclude Include="predef\library\std.h" />
    <ClInclude Include="predef\library\std\cxx.h" />
    <ClInclude Include="predef\library\std\dinkumware.h" />
    <ClInclude Include="predef\library\std\libcomo.h" />
    <ClInclude Include="predef\library\std\modena.h" />
    <ClInclude Include="predef\library\std\msl.h" />
    <ClInclude Include="predef\library\std\roguewave.h" />
    <ClInclude Include="predef\library\std\sgi.h" />
    <ClInclude Include="predef\library\std\stdcpp3.h" />
    <ClInclude Include="predef\library\std\stlport.h" />
    <ClInclude Include="predef\library\std\vacpp.h" />
    <ClInclude Include="predef\library\std\_prefix.h" />
    <ClInclude Include="predef\make.h" />
    <ClInclude Include="predef\os.h" />
    <ClInclude Include="predef\os\aix.h" />
    <ClInclude Include="predef\os\amigaos.h" />
    <ClInclude Include="predef\os\android.h" />
    <ClInclude Include="predef\os\beos.h" />
    <ClInclude Include="predef\os\bsd.h" />
    <ClInclude Include="predef\os\bsd\bsdi.h" />
    <ClInclude Include="predef\os\bsd\dragonfly.h" />
    <ClInclude Include="predef\os\bsd\free.h" />
    <ClInclude Include="predef\os\bsd\net.h" />
    <ClInclude Include="predef\os\bsd\open.h" />
    <ClInclude Include="predef\os\cygwin.h" />
    <ClInclude Include="predef\os\haiku.h" />
    <ClInclude Include="predef\os\hpux.h" />
    <ClInclude Include="predef\os\ios.h" />
    <ClInclude Include="predef\os\irix.h" />
    <ClInclude Include="predef\os\linux.h" />
    <ClInclude Include="predef\os\macos.h" />
    <ClInclude Include="predef\os\os400.h" />
    <ClInclude Include="predef\os\qnxnto.h" />
    <ClInclude Include="predef\os\solaris.h" />
    <ClInclude Include="predef\os\unix.h" />
    <ClInclude Include="predef\os\vms.h" />
    <ClInclude Include="predef\os\windows.h" />
    <ClInclude Include="predef\other.h" />
    <ClInclude Include="predef\other\endian.h" />
    <ClInclude Include="predef\platform.h" />
    <ClInclude Include="predef\platform\mingw.h" />
    <ClInclude Include="predef\platform\windows_desktop.h" />
    <ClInclude Include="predef\platform\windows_phone.h" />
    <ClInclude Include="predef\platform\windows_runtime.h" />
    <ClInclude Include="predef\platform\windows_store.h" />
    <ClInclude Include="predef\version.h" />
    <ClInclude Include="predef\version_number.h" />
    <ClInclude Include="preprocessor\arithmetic.hpp" />
    <ClInclude Include="preprocessor\arithmetic\add.hpp" />
    <ClInclude Include="preprocessor\arithmetic\dec.hpp" />
    <ClInclude Include="preprocessor\arithmetic\detail\div_base.hpp" />
    <ClInclude Include="preprocessor\arithmetic\div.hpp" />
    <ClInclude Include="preprocessor\arithmetic\inc.hpp" />
    <ClInclude Include="preprocessor\arithmetic\mod.hpp" />
    <ClInclude Include="preprocessor\arithmetic\mul.hpp" />
    <ClInclude Include="preprocessor\arithmetic\sub.hpp" />
    <ClInclude Include="preprocessor\array\data.hpp" />
    <ClInclude Include="preprocessor\array\elem.hpp" />
    <ClInclude Include="preprocessor\array\size.hpp" />
    <ClInclude Include="preprocessor\cat.hpp" />
    <ClInclude Include="preprocessor\comma_if.hpp" />
    <ClInclude Include="preprocessor\comparison\equal.hpp" />
    <ClInclude Include="preprocessor\comparison\less_equal.hpp" />
    <ClInclude Include="preprocessor\comparison\not_equal.hpp" />
    <ClInclude Include="preprocessor\config\config.hpp" />
    <ClInclude Include="preprocessor\control\deduce_d.hpp" />
    <ClInclude Include="preprocessor\control\detail\dmc\while.hpp" />
    <ClInclude Include="preprocessor\control\detail\edg\while.hpp" />
    <ClInclude Include="preprocessor\control\detail\msvc\while.hpp" />
    <ClInclude Include="preprocessor\control\detail\while.hpp" />
    <ClInclude Include="preprocessor\control\expr_if.hpp" />
    <ClInclude Include="preprocessor\control\expr_iif.hpp" />
    <ClInclude Include="preprocessor\control\if.hpp" />
    <ClInclude Include="preprocessor\control\iif.hpp" />
    <ClInclude Include="preprocessor\control\while.hpp" />
    <ClInclude Include="preprocessor\debug\error.hpp" />
    <ClInclude Include="preprocessor\dec.hpp" />
    <ClInclude Include="preprocessor\detail\auto_rec.hpp" />
    <ClInclude Include="preprocessor\detail\check.hpp" />
    <ClInclude Include="preprocessor\detail\dmc\auto_rec.hpp" />
    <ClInclude Include="preprocessor\detail\is_binary.hpp" />
    <ClInclude Include="preprocessor\detail\is_nullary.hpp" />
    <ClInclude Include="preprocessor\detail\split.hpp" />
    <ClInclude Include="preprocessor\empty.hpp" />
    <ClInclude Include="preprocessor\enum.hpp" />
    <ClInclude Include="preprocessor\enum_params.hpp" />
    <ClInclude Include="preprocessor\enum_params_with_a_default.hpp" />
    <ClInclude Include="preprocessor\enum_shifted_params.hpp" />
    <ClInclude Include="preprocessor\expr_if.hpp" />
    <ClInclude Include="preprocessor\facilities\detail\is_empty.hpp" />
    <ClInclude Include="preprocessor\facilities\empty.hpp" />
    <ClInclude Include="preprocessor\facilities\expand.hpp" />
    <ClInclude Include="preprocessor\facilities\identity.hpp" />
    <ClInclude Include="preprocessor\facilities\intercept.hpp" />
    <ClInclude Include="preprocessor\facilities\is_1.hpp" />
    <ClInclude Include="preprocessor\facilities\is_empty.hpp" />
    <ClInclude Include="preprocessor\facilities\is_empty_variadic.hpp" />
    <ClInclude Include="preprocessor\facilities\overload.hpp" />
    <ClInclude Include="preprocessor\for.hpp" />
    <ClInclude Include="preprocessor\identity.hpp" />
    <ClInclude Include="preprocessor\inc.hpp" />
    <ClInclude Include="preprocessor\iterate.hpp" />
    <ClInclude Include="preprocessor\iteration.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower1.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower2.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower3.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower4.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower5.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper1.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper2.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper3.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper4.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper5.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\finish.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\forward1.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\forward2.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\forward3.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\forward4.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\forward5.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse1.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse2.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse3.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse4.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse5.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\local.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\rlocal.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\self.hpp" />
    <ClInclude Include="preprocessor\iteration\detail\start.hpp" />
    <ClInclude Include="preprocessor\iteration\iterate.hpp" />
    <ClInclude Include="preprocessor\iteration\local.hpp" />
    <ClInclude Include="preprocessor\iteration\self.hpp" />
    <ClInclude Include="preprocessor\list\adt.hpp" />
    <ClInclude Include="preprocessor\list\detail\dmc\fold_left.hpp" />
    <ClInclude Include="preprocessor\list\detail\edg\fold_left.hpp" />
    <ClInclude Include="preprocessor\list\detail\edg\fold_right.hpp" />
    <ClInclude Include="preprocessor\list\detail\fold_left.hpp" />
    <ClInclude Include="preprocessor\list\detail\fold_right.hpp" />
    <ClInclude Include="preprocessor\list\fold_left.hpp" />
    <ClInclude Include="preprocessor\list\fold_right.hpp" />
    <ClInclude Include="preprocessor\list\for_each_i.hpp" />
    <ClInclude Include="preprocessor\list\reverse.hpp" />
    <ClInclude Include="preprocessor\logical\and.hpp" />
    <ClInclude Include="preprocessor\logical\bitand.hpp" />
    <ClInclude Include="preprocessor\logical\bool.hpp" />
    <ClInclude Include="preprocessor\logical\compl.hpp" />
    <ClInclude Include="preprocessor\logical\not.hpp" />
    <ClInclude Include="preprocessor\punctuation\comma.hpp" />
    <ClInclude Include="preprocessor\punctuation\comma_if.hpp" />
    <ClInclude Include="preprocessor\punctuation\detail\is_begin_parens.hpp" />
    <ClInclude Include="preprocessor\punctuation\is_begin_parens.hpp" />
    <ClInclude Include="preprocessor\repeat.hpp" />
    <ClInclude Include="preprocessor\repetition.hpp" />
    <ClInclude Include="preprocessor\repetition\deduce_r.hpp" />
    <ClInclude Include="preprocessor\repetition\deduce_z.hpp" />
    <ClInclude Include="preprocessor\repetition\detail\dmc\for.hpp" />
    <ClInclude Include="preprocessor\repetition\detail\edg\for.hpp" />
    <ClInclude Include="preprocessor\repetition\detail\for.hpp" />
    <ClInclude Include="preprocessor\repetition\detail\msvc\for.hpp" />
    <ClInclude Include="preprocessor\repetition\enum.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_binary_params.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_params.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_params_with_a_default.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_params_with_defaults.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_shifted.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_shifted_binary_params.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_shifted_params.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_trailing.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_trailing_binary_params.hpp" />
    <ClInclude Include="preprocessor\repetition\enum_trailing_params.hpp" />
    <ClInclude Include="preprocessor\repetition\for.hpp" />
    <ClInclude Include="preprocessor\repetition\repeat.hpp" />
    <ClInclude Include="preprocessor\repetition\repeat_from_to.hpp" />
    <ClInclude Include="preprocessor\selection\max.hpp" />
    <ClInclude Include="preprocessor\seq\cat.hpp" />
    <ClInclude Include="preprocessor\seq\detail\is_empty.hpp" />
    <ClInclude Include="preprocessor\seq\detail\split.hpp" />
    <ClInclude Include="preprocessor\seq\elem.hpp" />
    <ClInclude Include="preprocessor\seq\enum.hpp" />
    <ClInclude Include="preprocessor\seq\first_n.hpp" />
    <ClInclude Include="preprocessor\seq\fold_left.hpp" />
    <ClInclude Include="preprocessor\seq\for_each.hpp" />
    <ClInclude Include="preprocessor\seq\for_each_i.hpp" />
    <ClInclude Include="preprocessor\seq\for_each_product.hpp" />
    <ClInclude Include="preprocessor\seq\push_back.hpp" />
    <ClInclude Include="preprocessor\seq\rest_n.hpp" />
    <ClInclude Include="preprocessor\seq\seq.hpp" />
    <ClInclude Include="preprocessor\seq\size.hpp" />
    <ClInclude Include="preprocessor\seq\subseq.hpp" />
    <ClInclude Include="preprocessor\seq\transform.hpp" />
    <ClInclude Include="preprocessor\slot\detail\counter.hpp" />
    <ClInclude Include="preprocessor\slot\detail\def.hpp" />
    <ClInclude Include="preprocessor\slot\detail\shared.hpp" />
    <ClInclude Include="preprocessor\slot\detail\slot1.hpp" />
    <ClInclude Include="preprocessor\slot\detail\slot2.hpp" />
    <ClInclude Include="preprocessor\slot\detail\slot3.hpp" />
    <ClInclude Include="preprocessor\slot\detail\slot4.hpp" />
    <ClInclude Include="preprocessor\slot\detail\slot5.hpp" />
    <ClInclude Include="preprocessor\slot\slot.hpp" />
    <ClInclude Include="preprocessor\stringize.hpp" />
    <ClInclude Include="preprocessor\tuple\detail\is_single_return.hpp" />
    <ClInclude Include="preprocessor\tuple\eat.hpp" />
    <ClInclude Include="preprocessor\tuple\elem.hpp" />
    <ClInclude Include="preprocessor\tuple\rem.hpp" />
    <ClInclude Include="preprocessor\tuple\size.hpp" />
    <ClInclude Include="preprocessor\tuple\to_list.hpp" />
    <ClInclude Include="preprocessor\variadic\elem.hpp" />
    <ClInclude Include="preprocessor\variadic\size.hpp" />
    <ClInclude Include="random\detail\config.hpp" />
    <ClInclude Include="random\detail\const_mod.hpp" />
    <ClInclude Include="random\detail\disable_warnings.hpp" />
    <ClInclude Include="random\detail\enable_warnings.hpp" />
    <ClInclude Include="random\detail\generator_bits.hpp" />
    <ClInclude Include="random\detail\generator_seed_seq.hpp" />
    <ClInclude Include="random\detail\integer_log2.hpp" />
    <ClInclude Include="random\detail\large_arithmetic.hpp" />
    <ClInclude Include="random\detail\operators.hpp" />
    <ClInclude Include="random\detail\polynomial.hpp" />
    <ClInclude Include="random\detail\ptr_helper.hpp" />
    <ClInclude Include="random\detail\seed.hpp" />
    <ClInclude Include="random\detail\seed_impl.hpp" />
    <ClInclude Include="random\detail\signed_unsigned_tools.hpp" />
    <ClInclude Include="random\detail\uniform_int_float.hpp" />
    <ClInclude Include="random\mersenne_twister.hpp" />
    <ClInclude Include="random\traits.hpp" />
    <ClInclude Include="random\uniform_int.hpp" />
    <ClInclude Include="random\uniform_int_distribution.hpp" />
    <ClInclude Include="random\variate_generator.hpp" />
    <ClInclude Include="range\algorithm\equal.hpp" />
    <ClInclude Include="range\as_literal.hpp" />
    <ClInclude Include="range\begin.hpp" />
    <ClInclude Include="range\concepts.hpp" />
    <ClInclude Include="range\config.hpp" />
    <ClInclude Include="range\const_iterator.hpp" />
    <ClInclude Include="range\detail\as_literal.hpp" />
    <ClInclude Include="range\detail\begin.hpp" />
    <ClInclude Include="range\detail\common.hpp" />
    <ClInclude Include="range\detail\detail_str.hpp" />
    <ClInclude Include="range\detail\end.hpp" />
    <ClInclude Include="range\detail\extract_optional_type.hpp" />
    <ClInclude Include="range\detail\has_member_size.hpp" />
    <ClInclude Include="range\detail\implementation_help.hpp" />
    <ClInclude Include="range\detail\misc_concept.hpp" />
    <ClInclude Include="range\detail\msvc_has_iterator_workaround.hpp" />
    <ClInclude Include="range\detail\remove_extent.hpp" />
    <ClInclude Include="range\detail\safe_bool.hpp" />
    <ClInclude Include="range\detail\sfinae.hpp" />
    <ClInclude Include="range\detail\size_type.hpp" />
    <ClInclude Include="range\detail\str_types.hpp" />
    <ClInclude Include="range\detail\value_type.hpp" />
    <ClInclude Include="range\difference_type.hpp" />
    <ClInclude Include="range\distance.hpp" />
    <ClInclude Include="range\empty.hpp" />
    <ClInclude Include="range\end.hpp" />
    <ClInclude Include="range\functions.hpp" />
    <ClInclude Include="range\has_range_iterator.hpp" />
    <ClInclude Include="range\iterator.hpp" />
    <ClInclude Include="range\iterator_range.hpp" />
    <ClInclude Include="range\iterator_range_core.hpp" />
    <ClInclude Include="range\iterator_range_io.hpp" />
    <ClInclude Include="range\mutable_iterator.hpp" />
    <ClInclude Include="range\range_fwd.hpp" />
    <ClInclude Include="range\rbegin.hpp" />
    <ClInclude Include="range\rend.hpp" />
    <ClInclude Include="range\reverse_iterator.hpp" />
    <ClInclude Include="range\size.hpp" />
    <ClInclude Include="range\size_type.hpp" />
    <ClInclude Include="range\value_type.hpp" />
    <ClInclude Include="rational.hpp" />
    <ClInclude Include="ratio\config.hpp" />
    <ClInclude Include="ratio\detail\mpl\abs.hpp" />
    <ClInclude Include="ratio\detail\mpl\gcd.hpp" />
    <ClInclude Include="ratio\detail\mpl\lcm.hpp" />
    <ClInclude Include="ratio\detail\mpl\sign.hpp" />
    <ClInclude Include="ratio\detail\overflow_helpers.hpp" />
    <ClInclude Include="ratio\mpl\rational_c_tag.hpp" />
    <ClInclude Include="ratio\ratio.hpp" />
    <ClInclude Include="ratio\ratio_fwd.hpp" />
    <ClInclude Include="ref.hpp" />
    <ClInclude Include="scoped_array.hpp" />
    <ClInclude Include="scoped_ptr.hpp" />
    <ClInclude Include="shared_array.hpp" />
    <ClInclude Include="shared_ptr.hpp" />
    <ClInclude Include="signals2.hpp" />
    <ClInclude Include="signals2\connection.hpp" />
    <ClInclude Include="signals2\deconstruct.hpp" />
    <ClInclude Include="signals2\deconstruct_ptr.hpp" />
    <ClInclude Include="signals2\detail\auto_buffer.hpp" />
    <ClInclude Include="signals2\detail\foreign_ptr.hpp" />
    <ClInclude Include="signals2\detail\lwm_nop.hpp" />
    <ClInclude Include="signals2\detail\lwm_pthreads.hpp" />
    <ClInclude Include="signals2\detail\lwm_win32_cs.hpp" />
    <ClInclude Include="signals2\detail\null_output_iterator.hpp" />
    <ClInclude Include="signals2\detail\preprocessed_arg_type.hpp" />
    <ClInclude Include="signals2\detail\preprocessed_arg_type_template.hpp" />
    <ClInclude Include="signals2\detail\replace_slot_function.hpp" />
    <ClInclude Include="signals2\detail\result_type_wrapper.hpp" />
    <ClInclude Include="signals2\detail\signals_common.hpp" />
    <ClInclude Include="signals2\detail\signals_common_macros.hpp" />
    <ClInclude Include="signals2\detail\signal_template.hpp" />
    <ClInclude Include="signals2\detail\slot_call_iterator.hpp" />
    <ClInclude Include="signals2\detail\slot_groups.hpp" />
    <ClInclude Include="signals2\detail\slot_template.hpp" />
    <ClInclude Include="signals2\detail\tracked_objects_visitor.hpp" />
    <ClInclude Include="signals2\detail\unique_lock.hpp" />
    <ClInclude Include="signals2\detail\variadic_arg_type.hpp" />
    <ClInclude Include="signals2\detail\variadic_slot_invoker.hpp" />
    <ClInclude Include="signals2\dummy_mutex.hpp" />
    <ClInclude Include="signals2\expired_slot.hpp" />
    <ClInclude Include="signals2\last_value.hpp" />
    <ClInclude Include="signals2\mutex.hpp" />
    <ClInclude Include="signals2\optional_last_value.hpp" />
    <ClInclude Include="signals2\postconstructible.hpp" />
    <ClInclude Include="signals2\predestructible.hpp" />
    <ClInclude Include="signals2\preprocessed_signal.hpp" />
    <ClInclude Include="signals2\preprocessed_slot.hpp" />
    <ClInclude Include="signals2\shared_connection_block.hpp" />
    <ClInclude Include="signals2\signal.hpp" />
    <ClInclude Include="signals2\signal_base.hpp" />
    <ClInclude Include="signals2\signal_type.hpp" />
    <ClInclude Include="signals2\slot.hpp" />
    <ClInclude Include="signals2\slot_base.hpp" />
    <ClInclude Include="signals2\trackable.hpp" />
    <ClInclude Include="signals2\variadic_signal.hpp" />
    <ClInclude Include="signals2\variadic_slot.hpp" />
    <ClInclude Include="smart_ptr.hpp" />
    <ClInclude Include="smart_ptr\allocate_shared_array.hpp" />
    <ClInclude Include="smart_ptr\bad_weak_ptr.hpp" />
    <ClInclude Include="smart_ptr\detail\array_allocator.hpp" />
    <ClInclude Include="smart_ptr\detail\array_count_impl.hpp" />
    <ClInclude Include="smart_ptr\detail\array_traits.hpp" />
    <ClInclude Include="smart_ptr\detail\array_utility.hpp" />
    <ClInclude Include="smart_ptr\detail\lightweight_mutex.hpp" />
    <ClInclude Include="smart_ptr\detail\lwm_nop.hpp" />
    <ClInclude Include="smart_ptr\detail\lwm_pthreads.hpp" />
    <ClInclude Include="smart_ptr\detail\lwm_win32_cs.hpp" />
    <ClInclude Include="smart_ptr\detail\operator_bool.hpp" />
    <ClInclude Include="smart_ptr\detail\quick_allocator.hpp" />
    <ClInclude Include="smart_ptr\detail\shared_count.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_gcc_arm.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_nt.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_pool.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_pt.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_std_atomic.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_sync.hpp" />
    <ClInclude Include="smart_ptr\detail\spinlock_w32.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_convertible.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_acc_ia64.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_aix.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_clang.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_cw_ppc.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_ia64.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_mips.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_ppc.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_sparc.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_x86.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_nt.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_pt.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_snc_ps3.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_spin.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_std_atomic.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_sync.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_vacpp_ppc.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_base_w32.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_counted_impl.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_disable_deprecated.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_forward.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_has_sync.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_if_array.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_interlocked.hpp" />
    <ClInclude Include="smart_ptr\detail\sp_nullptr_t.hpp" />
    <ClInclude Include="smart_ptr\detail\yield_k.hpp" />
    <ClInclude Include="smart_ptr\enable_shared_from_this.hpp" />
    <ClInclude Include="smart_ptr\intrusive_ptr.hpp" />
    <ClInclude Include="smart_ptr\make_shared.hpp" />
    <ClInclude Include="smart_ptr\make_shared_array.hpp" />
    <ClInclude Include="smart_ptr\make_shared_object.hpp" />
    <ClInclude Include="smart_ptr\scoped_array.hpp" />
    <ClInclude Include="smart_ptr\scoped_ptr.hpp" />
    <ClInclude Include="smart_ptr\shared_array.hpp" />
    <ClInclude Include="smart_ptr\shared_ptr.hpp" />
    <ClInclude Include="smart_ptr\weak_ptr.hpp" />
    <ClInclude Include="static_assert.hpp" />
    <ClInclude Include="swap.hpp" />
    <ClInclude Include="system\api_config.hpp" />
    <ClInclude Include="system\config.hpp" />
    <ClInclude Include="system\detail\local_free_on_destruction.hpp" />
    <ClInclude Include="system\error_code.hpp" />
    <ClInclude Include="system\system_error.hpp" />
    <ClInclude Include="thread\condition.hpp" />
    <ClInclude Include="thread\condition_variable.hpp" />
    <ClInclude Include="thread\csbl\functional.hpp" />
    <ClInclude Include="thread\csbl\memory\allocator_arg.hpp" />
    <ClInclude Include="thread\csbl\memory\allocator_traits.hpp" />
    <ClInclude Include="thread\csbl\memory\config.hpp" />
    <ClInclude Include="thread\csbl\memory\pointer_traits.hpp" />
    <ClInclude Include="thread\csbl\memory\scoped_allocator.hpp" />
    <ClInclude Include="thread\csbl\memory\shared_ptr.hpp" />
    <ClInclude Include="thread\csbl\memory\unique_ptr.hpp" />
    <ClInclude Include="thread\csbl\tuple.hpp" />
    <ClInclude Include="thread\csbl\vector.hpp" />
    <ClInclude Include="thread\cv_status.hpp" />
    <ClInclude Include="thread\detail\config.hpp" />
    <ClInclude Include="thread\detail\delete.hpp" />
    <ClInclude Include="thread\detail\invoke.hpp" />
    <ClInclude Include="thread\detail\invoker.hpp" />
    <ClInclude Include="thread\detail\is_convertible.hpp" />
    <ClInclude Include="thread\detail\lockable_wrapper.hpp" />
    <ClInclude Include="thread\detail\make_tuple_indices.hpp" />
    <ClInclude Include="thread\detail\memory.hpp" />
    <ClInclude Include="thread\detail\move.hpp" />
    <ClInclude Include="thread\detail\nullary_function.hpp" />
    <ClInclude Include="thread\detail\platform.hpp" />
    <ClInclude Include="thread\detail\thread.hpp" />
    <ClInclude Include="thread\detail\thread_group.hpp" />
    <ClInclude Include="thread\detail\thread_heap_alloc.hpp" />
    <ClInclude Include="thread\detail\thread_interruption.hpp" />
    <ClInclude Include="thread\detail\tss_hooks.hpp" />
    <ClInclude Include="thread\detail\variadic_footer.hpp" />
    <ClInclude Include="thread\detail\variadic_header.hpp" />
    <ClInclude Include="thread\exceptional_ptr.hpp" />
    <ClInclude Include="thread\exceptions.hpp" />
    <ClInclude Include="thread\executor.hpp" />
    <ClInclude Include="thread\executors\executor.hpp" />
    <ClInclude Include="thread\executors\executor_adaptor.hpp" />
    <ClInclude Include="thread\executors\generic_executor_ref.hpp" />
    <ClInclude Include="thread\executors\work.hpp" />
    <ClInclude Include="thread\future.hpp" />
    <ClInclude Include="thread\futures\future_error.hpp" />
    <ClInclude Include="thread\futures\future_error_code.hpp" />
    <ClInclude Include="thread\futures\future_status.hpp" />
    <ClInclude Include="thread\futures\is_future_type.hpp" />
    <ClInclude Include="thread\futures\launch.hpp" />
    <ClInclude Include="thread\futures\wait_for_all.hpp" />
    <ClInclude Include="thread\futures\wait_for_any.hpp" />
    <ClInclude Include="thread\is_locked_by_this_thread.hpp" />
    <ClInclude Include="thread\lockable_traits.hpp" />
    <ClInclude Include="thread\locks.hpp" />
    <ClInclude Include="thread\lock_algorithms.hpp" />
    <ClInclude Include="thread\lock_guard.hpp" />
    <ClInclude Include="thread\lock_options.hpp" />
    <ClInclude Include="thread\lock_types.hpp" />
    <ClInclude Include="thread\mutex.hpp" />
    <ClInclude Include="thread\once.hpp" />
    <ClInclude Include="thread\recursive_mutex.hpp" />
    <ClInclude Include="thread\shared_mutex.hpp" />
    <ClInclude Include="thread\thread.hpp" />
    <ClInclude Include="thread\thread_only.hpp" />
    <ClInclude Include="thread\thread_time.hpp" />
    <ClInclude Include="thread\tss.hpp" />
    <ClInclude Include="thread\v2\thread.hpp" />
    <ClInclude Include="thread\win32\basic_recursive_mutex.hpp" />
    <ClInclude Include="thread\win32\basic_timed_mutex.hpp" />
    <ClInclude Include="thread\win32\condition_variable.hpp" />
    <ClInclude Include="thread\win32\interlocked_read.hpp" />
    <ClInclude Include="thread\win32\mutex.hpp" />
    <ClInclude Include="thread\win32\once.hpp" />
    <ClInclude Include="thread\win32\recursive_mutex.hpp" />
    <ClInclude Include="thread\win32\shared_mutex.hpp" />
    <ClInclude Include="thread\win32\thread_data.hpp" />
    <ClInclude Include="thread\win32\thread_heap_alloc.hpp" />
    <ClInclude Include="thread\win32\thread_primitives.hpp" />
    <ClInclude Include="thread\xtime.hpp" />
    <ClInclude Include="throw_exception.hpp" />
    <ClInclude Include="tokenizer.hpp" />
    <ClInclude Include="token_functions.hpp" />
    <ClInclude Include="token_iterator.hpp" />
    <ClInclude Include="tuple\detail\tuple_basic.hpp" />
    <ClInclude Include="tuple\tuple.hpp" />
    <ClInclude Include="type.hpp" />
    <ClInclude Include="type_index.hpp" />
    <ClInclude Include="type_index\ctti_type_index.hpp" />
    <ClInclude Include="type_index\detail\compile_time_type_info.hpp" />
    <ClInclude Include="type_index\detail\ctti_register_class.hpp" />
    <ClInclude Include="type_index\detail\stl_register_class.hpp" />
    <ClInclude Include="type_index\stl_type_index.hpp" />
    <ClInclude Include="type_index\type_index_facade.hpp" />
    <ClInclude Include="type_traits\add_const.hpp" />
    <ClInclude Include="type_traits\add_cv.hpp" />
    <ClInclude Include="type_traits\add_lvalue_reference.hpp" />
    <ClInclude Include="type_traits\add_pointer.hpp" />
    <ClInclude Include="type_traits\add_reference.hpp" />
    <ClInclude Include="type_traits\add_rvalue_reference.hpp" />
    <ClInclude Include="type_traits\add_volatile.hpp" />
    <ClInclude Include="type_traits\aligned_storage.hpp" />
    <ClInclude Include="type_traits\alignment_of.hpp" />
    <ClInclude Include="type_traits\common_type.hpp" />
    <ClInclude Include="type_traits\composite_traits.hpp" />
    <ClInclude Include="type_traits\conditional.hpp" />
    <ClInclude Include="type_traits\conversion_traits.hpp" />
    <ClInclude Include="type_traits\copy_cv.hpp" />
    <ClInclude Include="type_traits\cv_traits.hpp" />
    <ClInclude Include="type_traits\decay.hpp" />
    <ClInclude Include="type_traits\declval.hpp" />
    <ClInclude Include="type_traits\detail\common_arithmetic_type.hpp" />
    <ClInclude Include="type_traits\detail\common_type_impl.hpp" />
    <ClInclude Include="type_traits\detail\composite_member_pointer_type.hpp" />
    <ClInclude Include="type_traits\detail\composite_pointer_type.hpp" />
    <ClInclude Include="type_traits\detail\config.hpp" />
    <ClInclude Include="type_traits\detail\has_binary_operator.hpp" />
    <ClInclude Include="type_traits\detail\is_function_ptr_helper.hpp" />
    <ClInclude Include="type_traits\detail\is_function_ptr_tester.hpp" />
    <ClInclude Include="type_traits\detail\is_mem_fun_pointer_impl.hpp" />
    <ClInclude Include="type_traits\detail\is_mem_fun_pointer_tester.hpp" />
    <ClInclude Include="type_traits\detail\mp_defer.hpp" />
    <ClInclude Include="type_traits\detail\yes_no_type.hpp" />
    <ClInclude Include="type_traits\function_traits.hpp" />
    <ClInclude Include="type_traits\has_left_shift.hpp" />
    <ClInclude Include="type_traits\has_minus.hpp" />
    <ClInclude Include="type_traits\has_minus_assign.hpp" />
    <ClInclude Include="type_traits\has_nothrow_assign.hpp" />
    <ClInclude Include="type_traits\has_nothrow_constructor.hpp" />
    <ClInclude Include="type_traits\has_nothrow_copy.hpp" />
    <ClInclude Include="type_traits\has_plus.hpp" />
    <ClInclude Include="type_traits\has_plus_assign.hpp" />
    <ClInclude Include="type_traits\has_right_shift.hpp" />
    <ClInclude Include="type_traits\has_trivial_assign.hpp" />
    <ClInclude Include="type_traits\has_trivial_constructor.hpp" />
    <ClInclude Include="type_traits\has_trivial_copy.hpp" />
    <ClInclude Include="type_traits\has_trivial_destructor.hpp" />
    <ClInclude Include="type_traits\has_trivial_move_assign.hpp" />
    <ClInclude Include="type_traits\has_trivial_move_constructor.hpp" />
    <ClInclude Include="type_traits\integral_constant.hpp" />
    <ClInclude Include="type_traits\integral_promotion.hpp" />
    <ClInclude Include="type_traits\intrinsics.hpp" />
    <ClInclude Include="type_traits\is_abstract.hpp" />
    <ClInclude Include="type_traits\is_arithmetic.hpp" />
    <ClInclude Include="type_traits\is_array.hpp" />
    <ClInclude Include="type_traits\is_assignable.hpp" />
    <ClInclude Include="type_traits\is_base_and_derived.hpp" />
    <ClInclude Include="type_traits\is_base_of.hpp" />
    <ClInclude Include="type_traits\is_class.hpp" />
    <ClInclude Include="type_traits\is_const.hpp" />
    <ClInclude Include="type_traits\is_constructible.hpp" />
    <ClInclude Include="type_traits\is_convertible.hpp" />
    <ClInclude Include="type_traits\is_copy_constructible.hpp" />
    <ClInclude Include="type_traits\is_default_constructible.hpp" />
    <ClInclude Include="type_traits\is_destructible.hpp" />
    <ClInclude Include="type_traits\is_empty.hpp" />
    <ClInclude Include="type_traits\is_enum.hpp" />
    <ClInclude Include="type_traits\is_float.hpp" />
    <ClInclude Include="type_traits\is_floating_point.hpp" />
    <ClInclude Include="type_traits\is_function.hpp" />
    <ClInclude Include="type_traits\is_fundamental.hpp" />
    <ClInclude Include="type_traits\is_integral.hpp" />
    <ClInclude Include="type_traits\is_lvalue_reference.hpp" />
    <ClInclude Include="type_traits\is_member_function_pointer.hpp" />
    <ClInclude Include="type_traits\is_member_pointer.hpp" />
    <ClInclude Include="type_traits\is_nothrow_move_assignable.hpp" />
    <ClInclude Include="type_traits\is_nothrow_move_constructible.hpp" />
    <ClInclude Include="type_traits\is_pod.hpp" />
    <ClInclude Include="type_traits\is_pointer.hpp" />
    <ClInclude Include="type_traits\is_polymorphic.hpp" />
    <ClInclude Include="type_traits\is_reference.hpp" />
    <ClInclude Include="type_traits\is_rvalue_reference.hpp" />
    <ClInclude Include="type_traits\is_same.hpp" />
    <ClInclude Include="type_traits\is_scalar.hpp" />
    <ClInclude Include="type_traits\is_signed.hpp" />
    <ClInclude Include="type_traits\is_stateless.hpp" />
    <ClInclude Include="type_traits\is_union.hpp" />
    <ClInclude Include="type_traits\is_unsigned.hpp" />
    <ClInclude Include="type_traits\is_void.hpp" />
    <ClInclude Include="type_traits\is_volatile.hpp" />
    <ClInclude Include="type_traits\make_signed.hpp" />
    <ClInclude Include="type_traits\make_unsigned.hpp" />
    <ClInclude Include="type_traits\remove_all_extents.hpp" />
    <ClInclude Include="type_traits\remove_bounds.hpp" />
    <ClInclude Include="type_traits\remove_const.hpp" />
    <ClInclude Include="type_traits\remove_cv.hpp" />
    <ClInclude Include="type_traits\remove_extent.hpp" />
    <ClInclude Include="type_traits\remove_pointer.hpp" />
    <ClInclude Include="type_traits\remove_reference.hpp" />
    <ClInclude Include="type_traits\remove_volatile.hpp" />
    <ClInclude Include="type_traits\type_identity.hpp" />
    <ClInclude Include="type_traits\type_with_alignment.hpp" />
    <ClInclude Include="utility.hpp" />
    <ClInclude Include="utility\addressof.hpp" />
    <ClInclude Include="utility\base_from_member.hpp" />
    <ClInclude Include="utility\binary.hpp" />
    <ClInclude Include="utility\compare_pointees.hpp" />
    <ClInclude Include="utility\declval.hpp" />
    <ClInclude Include="utility\detail\result_of_iterate.hpp" />
    <ClInclude Include="utility\enable_if.hpp" />
    <ClInclude Include="utility\identity_type.hpp" />
    <ClInclude Include="utility\result_of.hpp" />
    <ClInclude Include="utility\swap.hpp" />
    <ClInclude Include="uuid\detail\config.hpp" />
    <ClInclude Include="uuid\detail\uuid_generic.hpp" />
    <ClInclude Include="uuid\detail\uuid_x86.hpp" />
    <ClInclude Include="uuid\random_generator.hpp" />
    <ClInclude Include="uuid\seed_rng.hpp" />
    <ClInclude Include="uuid\sha1.hpp" />
    <ClInclude Include="uuid\uuid.hpp" />
    <ClInclude Include="variant\apply_visitor.hpp" />
    <ClInclude Include="variant\detail\apply_visitor_binary.hpp" />
    <ClInclude Include="variant\detail\apply_visitor_delayed.hpp" />
    <ClInclude Include="variant\detail\apply_visitor_unary.hpp" />
    <ClInclude Include="variant\detail\backup_holder.hpp" />
    <ClInclude Include="variant\detail\cast_storage.hpp" />
    <ClInclude Include="variant\detail\config.hpp" />
    <ClInclude Include="variant\detail\enable_recursive_fwd.hpp" />
    <ClInclude Include="variant\detail\forced_return.hpp" />
    <ClInclude Include="variant\detail\generic_result_type.hpp" />
    <ClInclude Include="variant\detail\hash_variant.hpp" />
    <ClInclude Include="variant\detail\has_result_type.hpp" />
    <ClInclude Include="variant\detail\initializer.hpp" />
    <ClInclude Include="variant\detail\make_variant_list.hpp" />
    <ClInclude Include="variant\detail\move.hpp" />
    <ClInclude Include="variant\detail\over_sequence.hpp" />
    <ClInclude Include="variant\detail\substitute_fwd.hpp" />
    <ClInclude Include="variant\detail\variant_io.hpp" />
    <ClInclude Include="variant\detail\visitation_impl.hpp" />
    <ClInclude Include="variant\recursive_wrapper_fwd.hpp" />
    <ClInclude Include="variant\static_visitor.hpp" />
    <ClInclude Include="variant\variant.hpp" />
    <ClInclude Include="variant\variant_fwd.hpp" />
    <ClInclude Include="version.hpp" />
    <ClInclude Include="visit_each.hpp" />
    <ClInclude Include="weak_ptr.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="libs\atomic\src\lockpool.cpp" />
    <ClCompile Include="libs\date_time\src\gregorian\date_generators.cpp" />
    <ClCompile Include="libs\date_time\src\gregorian\gregorian_types.cpp" />
    <ClCompile Include="libs\date_time\src\gregorian\greg_month.cpp" />
    <ClCompile Include="libs\date_time\src\gregorian\greg_weekday.cpp" />
    <ClCompile Include="libs\date_time\src\posix_time\posix_time_types.cpp" />
    <ClCompile Include="libs\exception\src\clone_current_exception_non_intrusive.cpp" />
    <ClCompile Include="libs\filesystem\src\codecvt_error_category.cpp" />
    <ClCompile Include="libs\filesystem\src\operations.cpp" />
    <ClCompile Include="libs\filesystem\src\path.cpp" />
    <ClCompile Include="libs\filesystem\src\path_traits.cpp" />
    <ClCompile Include="libs\filesystem\src\portability.cpp" />
    <ClCompile Include="libs\filesystem\src\unique_path.cpp" />
    <ClCompile Include="libs\filesystem\src\utf8_codecvt_facet.cpp" />
    <ClCompile Include="libs\filesystem\src\windows_file_codecvt.cpp" />
    <ClCompile Include="libs\iostreams\src\file_descriptor.cpp" />
    <ClCompile Include="libs\iostreams\src\mapped_file.cpp" />
    <ClCompile Include="libs\smart_ptr\src\sp_collector.cpp" />
    <ClCompile Include="libs\smart_ptr\src\sp_debug_hooks.cpp" />
    <ClCompile Include="libs\system\src\error_code.cpp" />
    <ClCompile Include="libs\thread\src\future.cpp" />
    <ClCompile Include="libs\thread\src\win32\thread.cpp" />
    <ClCompile Include="libs\thread\src\win32\tss_dll.cpp" />
    <ClCompile Include="libs\thread\src\win32\tss_pe.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>