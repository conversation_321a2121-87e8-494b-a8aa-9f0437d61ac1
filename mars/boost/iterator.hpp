//  (C) Copyright Be<PERSON> 2000. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_ITERATOR_HPP
#define BOOST_ITERATOR_HPP

// This header is obsolete and will be deprecated.

#include <iterator>
#include <cstddef>           // std::ptrdiff_t

namespace mars_boost {} namespace boost = mars_boost; namespace mars_boost
{

using std::iterator;

} // namespace mars_boost

#endif // BOOST_ITERATOR_HPP
