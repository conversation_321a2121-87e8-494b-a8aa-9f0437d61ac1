//-----------------------------------------------------------------------------
// boost blank_fwd.hpp header file
// See http://www.boost.org for updates, documentation, and revision history.
//-----------------------------------------------------------------------------
//
// Copyright (c) 2003
// <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_BLANK_FWD_HPP
#define BOOST_BLANK_FWD_HPP

namespace mars_boost {} namespace boost = mars_boost; namespace mars_boost {

struct blank;

} // namespace mars_boost

#endif // BOOST_BLANK_FWD_HPP
