<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="atomic\atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\atomic_flag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\capabilities.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\atomic_flag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\atomic_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\bitwise_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_alpha.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_ppc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_sparc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_sync.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_gcc_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_linux_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_msvc_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_msvc_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\caps_windows.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\interlocked.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\int_sizes.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\link.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\lockpool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\operations.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\operations_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\operations_lockfree.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_cas_based.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_emulated.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_extending_cas_based.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_alpha.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_ppc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_sparc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_sync.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_gcc_x86_dcas.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_linux_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_msvc_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_msvc_common.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_msvc_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\ops_windows.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\pause.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\platform.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\detail\storage_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic\fences.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\bind_cc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\bind_mf2_cc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\bind_mf_cc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\bind_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\mem_fn.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\mem_fn_cc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\mem_fn_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\mem_fn_vw.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind\storage.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\ceil.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\chrono.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\clock_string.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\chrono.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\mac\chrono.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\mac\process_cpu_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\posix\chrono.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\posix\process_cpu_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\process_cpu_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\thread_clock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\win\chrono.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\win\process_cpu_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\inlined\win\thread_clock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\is_evenly_divisible_by.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\static_assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\detail\system.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\duration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\process_cpu_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\system_clocks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\thread_clock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chrono\time_point.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\backward_compatibility.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\borland.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\concept_def.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\concept_undef.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\general.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\has_constraints.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\detail\msvc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept\usage.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi\borland_prefix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi\borland_suffix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi\msvc_prefix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi\msvc_suffix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi_prefix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\abi_suffix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\auto_link.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\borland.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\clang.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\codegear.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\comeau.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\common_edg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\compaq_cxx.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\cray.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\digitalmars.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\gcc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\gcc_xml.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\greenhills.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\hp_acc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\intel.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\kai.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\metrowerks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\mpw.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\nvcc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\pathscale.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\pgi.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\sgi_mipspro.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\sunpro_cc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\vacpp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\visualc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\compiler\xlcpp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\no_tr1\cmath.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\no_tr1\complex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\no_tr1\functional.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\no_tr1\memory.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\no_tr1\utility.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\aix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\amigaos.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\beos.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\bsd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\cloudabi.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\cray.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\cygwin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\haiku.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\hpux.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\irix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\linux.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\macos.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\qnxnto.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\solaris.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\symbian.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\vms.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\vxworks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\platform\win32.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\posix_features.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\requires_threads.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\select_compiler_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\select_platform_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\select_stdlib_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\dinkumware.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\libcomo.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\libcpp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\libstdcpp3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\modena.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\msl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\roguewave.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\sgi.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\stlport.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\stdlib\vacpp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\suffix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\user.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config\warning_disable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\allocator_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\container_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\addressof.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\advanced_insert_int.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\algorithm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\allocation_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\alloc_helpers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\config_begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\config_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\copy_move_algo.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\destroyers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\dispatch_uses_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\iterators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\iterator_to_raw_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\min_max.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\mpl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\next_capacity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\pair.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\placement_new.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\std_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\to_raw_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\type_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\value_init.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\variadic_templates_tools.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\version_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\detail\workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\new_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\scoped_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\scoped_allocator_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\throw_exception.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\uses_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\uses_allocator_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="container\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\addressof.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\checked_delete.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\demangle.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\enable_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\explicit_operator_bool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\ignore_unused.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\is_same.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\noncopyable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\no_exceptions_support.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\ref.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\scoped_enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\swap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="core\typeinfo.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\adjust_functors.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\compiler_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\constrained_value.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_clock_device.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_defs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_duration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_duration_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_formatting.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_formatting_limited.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_formatting_locales.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_format_simple.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_generators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_names_put.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\date_parsing.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\dst_rules.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\filetime_functions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\conversion.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\formatters.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\formatters_limited.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\gregorian_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_calendar.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_date.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_day.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_day_of_year.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_duration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_duration_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_facet.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_month.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_weekday.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_year.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\greg_ymd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian\parsers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\gregorian_calendar.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\int_adapter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\iso_format.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\locale_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\microsec_time_clock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\parse_format_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\period.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\conversion.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\date_duration_operators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\posix_time_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\posix_time_duration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\posix_time_system.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\posix_time_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\ptime.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\posix_time\time_period.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\special_defs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_clock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_defs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_duration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_resolution_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_system_counted.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\time_system_split.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\wrapping_int.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\year_month_day.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\all_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\any_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\copy_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\copy_n.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\find_if_not.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\iota.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\is_partitioned.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\is_permutation.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\is_sorted.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\none_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\one_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\partition_copy.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\partition_point.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\classification.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\compare.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\concept.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\constants.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\detail\classification.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\detail\finder.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\detail\find_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\detail\trim.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\detail\util.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\finder.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\find_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\iter_find.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\predicate_facade.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\split.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="algorithm\string\trim.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="align\align.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="align\detail\address.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="align\detail\align.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="align\detail\align_cxx11.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="align\detail\is_alignment.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer\common_factor_rt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer\integer_log2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer\integer_mask.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer\static_log2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\algorithm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\config_begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\config_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\has_member_function_callable_with.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\minimal_pair_header.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\mpl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\pointer_element.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\reverse_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\std_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\to_raw_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\detail\workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\pointer_rebind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive\pointer_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="io\detail\quoted_manip.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="io\ios_state.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\categories.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\char_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\checked_operations.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\close.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\concepts.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\constants.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\adapter\non_blocking_adapter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\adapter\range_adapter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\bool_trait_def.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\buffer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\char_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\auto_link.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\bzip2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\codecvt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\disable_warnings.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\dyn_link.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\enable_warnings.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\fpos.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\limits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\rtl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\unreachable_return.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\wide_streams.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\windows_posix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\config\zlib.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\default_arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\dispatch.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\enable_if_stream.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\error.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\file_handle.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\ios.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\is_iterator_range.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\path.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\select.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\select_by_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\streambuf.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\system_failure.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\template_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\vc6\close.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\vc6\read.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\vc6\write.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\detail\wrap_unwrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\device\back_inserter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\device\file_descriptor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\device\mapped_file.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\filter\bzip2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\filter\gzip.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\filter\symmetric.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\filter\zlib.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\flush.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\get.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\imbue.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\input_sequence.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\operations.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\operations_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\optimal_buffer_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\output_sequence.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\pipeline.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\positioning.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\put.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\putback.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\read.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\seek.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\traits_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iostreams\write.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\detail\config_def.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\detail\config_undef.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\detail\enable_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\detail\facade_iterator_category.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\interoperable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\iterator_adaptor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\iterator_categories.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\iterator_concepts.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\iterator_facade.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\iterator_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\minimum_category.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\reverse_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator\transform_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\bad_lexical_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\converter_lexical.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\converter_lexical_streams.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\converter_numeric.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\inf_nan.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\is_character.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\lcast_char_constants.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\lcast_unsigned_converters.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\detail\widest_char.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast\try_lexical_convert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\atomic_redef_macros.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\atomic_undef_macros.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\basic_pointerbuf.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\bitmask.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\call_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\container_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\endian.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\fenv.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\indirect_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\interlocked.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\is_xxx.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\lcast_precision.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\lightweight_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\no_exceptions_support.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\reference_content.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\scoped_enum_emulation.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\sp_typeinfo.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\templated_streams.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\utf8_codecvt_facet.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\basic_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\crypt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\detail\cast_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\GetCurrentProcess.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\GetCurrentThread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\GetLastError.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\GetProcessTimes.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\GetThreadTimes.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\process.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\time.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\winapi\timers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="detail\workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\current_exception_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\clone_current_exception.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\error_info_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\exception_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\is_output_streamable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\object_hex_dump.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\detail\type_info.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\diagnostic_information.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\exception.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\get_error_info.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\info.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\to_string.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception\to_string_stub.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\convenience.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\detail\utf8_codecvt_facet.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\fstream.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\operations.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\path.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\path_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem\string_file.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\detail\function_iterate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\detail\maybe_include.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\detail\prologue.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function6.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function7.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function8.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function9.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function\function_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\detail\float_functions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\detail\hash_float.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\detail\limits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\extensions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\hash.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash\hash_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="functional\hash_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\apply_visitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\apply_visitor_binary.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\apply_visitor_delayed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\apply_visitor_unary.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\backup_holder.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\cast_storage.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\enable_recursive_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\forced_return.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\generic_result_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\hash_variant.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\has_result_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\initializer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\make_variant_list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\move.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\over_sequence.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\substitute_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\variant_io.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\detail\visitation_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\recursive_wrapper_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\static_visitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\variant.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="variant\variant_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="aligned_storage.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="any.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="blank.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="blank_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\common_factor_ct.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\policies\policy.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\special_functions\detail\fp_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\special_functions\detail\round_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\special_functions\fpclassify.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\special_functions\math_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\special_functions\sign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\tools\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\tools\promotion.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\tools\real_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math\tools\user.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\adl_move_swap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\algorithm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\core.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\default_delete.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\config_begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\config_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\fwd_macros.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\iterator_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\meta_utils.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\meta_utils_core.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\move_helpers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\std_ns_begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\std_ns_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\type_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\unique_ptr_meta_utils.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\detail\workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\make_unique.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\move.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\unique_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\utility.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="move\utility_core.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\advance.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\advance_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\always.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\arg_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\at.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\at_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\adl_barrier.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\arg_typedef.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\arithmetic_op.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\arity_spec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\at_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\begin_end_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\clear_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\common_name_wknd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\comparison_op.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\adl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\arrays.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\bcc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\compiler.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\dependent_nttp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\dmc_ambiguous_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\dtp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\eti.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\forwarding.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\gcc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\gpu.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\has_apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\has_xxx.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\integral.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\intel.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\msvc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\msvc_typename.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\nttp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\operators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\overload_resolution.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\pp_counter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\preprocessor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\static_constant.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\ttp.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\typeof.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\use_preprocessed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\config\workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\contains_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\count_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\empty_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\find_if_pred.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\fold_impl_body.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\front_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_key_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_rebind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\has_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\include_preprocessed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\inserter_algorithm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\insert_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\integral_wrapper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\is_msvc_eti_arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\iter_apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\lambda_arity_param.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\lambda_spec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\lambda_support.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\largest_int.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\logical_op.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\msvc_dtw.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\msvc_eti_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\msvc_is_class.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\msvc_never_true.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\msvc_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\na.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\na_assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\na_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\na_spec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\nested_type_wknd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\nttp_decl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\numeric_cast_utils.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\numeric_op.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\O1_size_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\overload_names.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc551\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\bcc_pre590\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\dmc\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\gcc\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc60\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\msvc70\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\mwcw\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ctps\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\no_ttp\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\advance_backward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\advance_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\apply_wrap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\basic_bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\bitxor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\deque.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\divides.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\full_lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\inherit.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\iter_fold_if_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\lambda_no_ctps.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\list_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\map.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\modulus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\reverse_iter_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\set_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\shift_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\shift_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\unpack_args.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessed\plain\vector_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\add.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\default_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\def_params_tail.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\ext_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\filter_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\partial_spec_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\range.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\repeat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\sub.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\preprocessor\tuple.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\ptr_to_ref.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\push_back_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\push_front_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\reverse_fold_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\reverse_fold_impl_body.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\sequence_wrapper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\size_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\static_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\template_arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\template_arity_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\traits_lambda_spec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\type_wrapper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\value_wknd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\aux_\yes_no.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\back_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\back_inserter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\begin_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\begin_end_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\bind.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\bind_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\bool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\bool_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\clear.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\clear_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\comparison.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\contains.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\contains_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\deref.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\distance.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\distance_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\empty_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\erase_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\erase_key_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\eval_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\find.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\find_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\fold.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\front_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\front_inserter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\greater.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\greater_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\has_key.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\has_key_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\has_xxx.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\identity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\insert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\inserter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\insert_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\insert_range_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\int.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\integral_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\integral_c_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\integral_c_tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\int_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\is_placeholder.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\is_sequence.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\iterator_category.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\iterator_range.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\iterator_tags.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\iter_fold.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\iter_fold_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\key_type_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\lambda.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\lambda_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\less.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\limits\arity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\limits\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\limits\unrolling.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\limits\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\begin_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\clear.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\include_preprocessed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\item.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\numbered.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\numbered_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\O1_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\pop_front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\preprocessed\plain\list50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\push_back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\push_front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\aux_\tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list0_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list\list50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\logical.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\long.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\long_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\max_element.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\min_max.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\multiplies.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\negate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\next.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\next_prior.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\not.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\not_equal_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\numeric_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\O1_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\O1_size_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\or.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\pair.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\pair_view.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\placeholders.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\pop_back_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\pop_front_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\prior.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\protect.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\push_back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\push_back_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\push_front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\push_front_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\quote.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\remove_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\reverse_fold.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\same_as.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\sequence_tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\sequence_tag_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\at_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\begin_end_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\clear_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\empty_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\erase_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\erase_key_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\has_key_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\insert_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\insert_range_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\item.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\key_type_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\set0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\size_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\aux_\value_type_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\set\set0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\sizeof.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\size_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\size_t.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\size_t_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\times.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\transform.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\value_type_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\at.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\begin_end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\clear.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\include_preprocessed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\item.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\numbered.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\numbered_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\O1_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\pop_back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\pop_front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\no_ctps\vector50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\plain\vector50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\preprocessed\typeof_based\vector50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\push_back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\push_front.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\aux_\vector0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector0_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector10.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector10_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector20.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector20_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector30.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector30_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector40.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector40_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector50.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector\vector50_c.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\void.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mpl\void_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="multi_index\detail\scope_guard.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\bounds.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\conversion_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\converter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\converter_policies.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\bounds.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\conversion_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\converter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\int_float_mixture.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\is_subranged.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\meta.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\numeric_cast_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\old_numeric_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\preprocessed\numeric_cast_traits_common.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\preprocessed\numeric_cast_traits_long_long.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\sign_mixture.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\detail\udt_builtin_mixture.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\int_float_mixture_enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\numeric_cast_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\sign_mixture_enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="numeric\conversion\udt_builtin_mixture_enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="optional\bad_optional_access.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="optional\optional.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="optional\optional_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\arg_list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\default.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\is_maybe.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\overloads.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\parameter_requirements.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\parenthesized_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\preprocessor\flatten.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\preprocessor\for_each.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\result_of0.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\set.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\tagged_argument.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\template_keyword.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\unwrap_cv_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\void.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\aux_\yesno.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\binding.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\keyword.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\macros.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\match.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\name.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\parameters.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\preprocessor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter\value_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pending\integer_log2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\alpha.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\arm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\blackfin.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\convex.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\ia64.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\m68k.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\mips.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\parisc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\ppc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\pyramid.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\rs6k.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\sparc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\superh.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\sys370.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\sys390.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\x86\32.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\x86\64.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\x86.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture\z.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\architecture.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\borland.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\clang.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\comeau.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\compaq.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\diab.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\digitalmars.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\dignus.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\edg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\ekopath.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\gcc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\gcc_xml.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\greenhills.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\hp_acc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\iar.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\ibm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\intel.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\kai.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\llvm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\metaware.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\metrowerks.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\microtec.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\mpw.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\palm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\pgi.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\sgi_mipspro.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\sunpro.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\tendra.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\visualc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler\watcom.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\compiler.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\comp_detected.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\endian_compat.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\os_detected.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\platform_detected.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\test.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\_cassert.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\detail\_exception.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\arm\versions.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\arm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\ppc\versions.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\ppc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\x86\versions.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\x86.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\x86_amd\versions.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd\x86_amd.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware\simd.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\hardware.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\language\objc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\language\stdc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\language\stdcpp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\language.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c\gnu.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c\uc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c\vms.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c\zos.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c\_prefix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\c.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\cxx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\dinkumware.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\libcomo.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\modena.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\msl.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\roguewave.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\sgi.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\stdcpp3.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\stlport.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\vacpp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std\_prefix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library\std.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\library.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\make.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\aix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\amigaos.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\android.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\beos.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd\bsdi.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd\dragonfly.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd\free.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd\net.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd\open.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\bsd.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\cygwin.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\haiku.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\hpux.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\ios.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\irix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\linux.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\macos.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\os400.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\qnxnto.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\solaris.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\unix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\vms.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os\windows.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\os.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\other\endian.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\other.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform\mingw.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform\windows_desktop.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform\windows_phone.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform\windows_runtime.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform\windows_store.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\platform.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\version.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef\version_number.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\add.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\dec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\detail\div_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\div.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\inc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\mod.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\mul.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic\sub.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\arithmetic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\array\data.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\array\elem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\array\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\cat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\comma_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\comparison\equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\comparison\less_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\comparison\not_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\config\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\deduce_d.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\detail\dmc\while.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\detail\edg\while.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\detail\msvc\while.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\detail\while.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\expr_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\expr_iif.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\iif.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\control\while.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\debug\error.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\dec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\auto_rec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\check.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\dmc\auto_rec.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\is_binary.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\is_nullary.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\detail\split.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\enum_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\enum_params_with_a_default.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\enum_shifted_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\expr_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\detail\is_empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\expand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\identity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\intercept.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\is_1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\is_empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\is_empty_variadic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\facilities\overload.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\identity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\inc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iterate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\lower5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\bounds\upper5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\finish.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\forward1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\forward2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\forward3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\forward4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\forward5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\iter\reverse5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\local.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\rlocal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\self.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\detail\start.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\iterate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\local.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration\self.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\iteration.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\adt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\detail\dmc\fold_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\detail\edg\fold_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\detail\edg\fold_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\detail\fold_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\detail\fold_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\fold_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\fold_right.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\for_each_i.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\list\reverse.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\logical\and.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\logical\bitand.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\logical\bool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\logical\compl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\logical\not.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\punctuation\comma.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\punctuation\comma_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\punctuation\detail\is_begin_parens.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\punctuation\is_begin_parens.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repeat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\deduce_r.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\deduce_z.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\detail\dmc\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\detail\edg\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\detail\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\detail\msvc\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_binary_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_params_with_a_default.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_params_with_defaults.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_shifted.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_shifted_binary_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_shifted_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_trailing.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_trailing_binary_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\enum_trailing_params.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\for.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\repeat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition\repeat_from_to.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\repetition.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\selection\max.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\cat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\detail\is_empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\detail\split.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\elem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\first_n.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\fold_left.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\for_each.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\for_each_i.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\for_each_product.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\push_back.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\rest_n.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\seq.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\subseq.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\seq\transform.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\counter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\def.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\shared.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\slot1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\slot2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\slot3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\slot4.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\detail\slot5.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\slot\slot.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\stringize.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\detail\is_single_return.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\eat.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\elem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\rem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\tuple\to_list.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\variadic\elem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="preprocessor\variadic\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\const_mod.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\disable_warnings.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\enable_warnings.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\generator_bits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\generator_seed_seq.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\integer_log2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\large_arithmetic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\operators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\polynomial.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\ptr_helper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\seed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\seed_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\signed_unsigned_tools.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\detail\uniform_int_float.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\mersenne_twister.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\uniform_int.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\uniform_int_distribution.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="random\variate_generator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\algorithm\equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\as_literal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\concepts.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\const_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\as_literal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\begin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\common.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\detail_str.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\extract_optional_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\has_member_size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\implementation_help.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\misc_concept.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\msvc_has_iterator_workaround.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\remove_extent.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\safe_bool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\sfinae.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\size_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\str_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\detail\value_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\difference_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\distance.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\end.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\functions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\has_range_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\iterator_range.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\iterator_range_core.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\iterator_range_io.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\mutable_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\range_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\rbegin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\rend.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\reverse_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\size.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\size_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="range\value_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\detail\mpl\abs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\detail\mpl\gcd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\detail\mpl\lcm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\detail\mpl\sign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\detail\overflow_helpers.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\mpl\rational_c_tag.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\ratio.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ratio\ratio_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\connection.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\deconstruct.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\deconstruct_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\auto_buffer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\foreign_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\lwm_nop.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\lwm_pthreads.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\lwm_win32_cs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\null_output_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\preprocessed_arg_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\preprocessed_arg_type_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\replace_slot_function.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\result_type_wrapper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\signals_common.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\signals_common_macros.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\signal_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\slot_call_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\slot_groups.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\slot_template.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\tracked_objects_visitor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\unique_lock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\variadic_arg_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\detail\variadic_slot_invoker.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\dummy_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\expired_slot.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\last_value.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\optional_last_value.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\postconstructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\predestructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\preprocessed_signal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\preprocessed_slot.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\shared_connection_block.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\signal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\signal_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\signal_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\slot.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\slot_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\trackable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\variadic_signal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2\variadic_slot.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\allocate_shared_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\bad_weak_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\array_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\array_count_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\array_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\array_utility.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\lightweight_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\lwm_nop.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\lwm_pthreads.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\lwm_win32_cs.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\operator_bool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\quick_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\shared_count.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_gcc_arm.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_nt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_pool.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_pt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_std_atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_sync.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\spinlock_w32.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_convertible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_acc_ia64.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_aix.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_clang.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_cw_ppc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_ia64.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_mips.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_ppc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_sparc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_gcc_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_nt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_pt.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_snc_ps3.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_spin.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_std_atomic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_sync.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_vacpp_ppc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_base_w32.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_counted_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_disable_deprecated.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_forward.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_has_sync.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_if_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_interlocked.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\sp_nullptr_t.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\detail\yield_k.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\enable_shared_from_this.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\intrusive_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\make_shared.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\make_shared_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\make_shared_object.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\scoped_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\scoped_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\shared_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\shared_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr\weak_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="system\api_config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="system\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="system\detail\local_free_on_destruction.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="system\error_code.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="system\system_error.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\condition.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\condition_variable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\functional.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\allocator_arg.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\allocator_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\pointer_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\scoped_allocator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\shared_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\memory\unique_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\tuple.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\csbl\vector.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\cv_status.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\delete.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\invoke.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\invoker.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\is_convertible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\lockable_wrapper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\make_tuple_indices.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\memory.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\move.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\nullary_function.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\platform.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\thread_group.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\thread_heap_alloc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\thread_interruption.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\tss_hooks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\variadic_footer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\detail\variadic_header.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\exceptional_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\exceptions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\executor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\executors\executor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\executors\executor_adaptor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\executors\generic_executor_ref.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\executors\work.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\future.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\future_error.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\future_error_code.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\future_status.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\is_future_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\launch.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\wait_for_all.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\futures\wait_for_any.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\is_locked_by_this_thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\lockable_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\locks.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\lock_algorithms.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\lock_guard.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\lock_options.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\lock_types.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\once.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\recursive_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\shared_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\thread_only.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\thread_time.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\tss.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\v2\thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\basic_recursive_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\basic_timed_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\condition_variable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\interlocked_read.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\once.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\recursive_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\shared_mutex.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\thread_data.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\thread_heap_alloc.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\win32\thread_primitives.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="thread\xtime.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="tuple\detail\tuple_basic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="tuple\tuple.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\ctti_type_index.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\detail\compile_time_type_info.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\detail\ctti_register_class.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\detail\stl_register_class.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\stl_type_index.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index\type_index_facade.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_const.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_cv.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_lvalue_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_rvalue_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\add_volatile.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\aligned_storage.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\alignment_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\common_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\composite_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\conditional.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\conversion_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\copy_cv.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\cv_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\decay.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\declval.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\common_arithmetic_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\common_type_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\composite_member_pointer_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\composite_pointer_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\has_binary_operator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\is_function_ptr_helper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\is_function_ptr_tester.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\is_mem_fun_pointer_impl.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\is_mem_fun_pointer_tester.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\mp_defer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\detail\yes_no_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\function_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_left_shift.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_minus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_minus_assign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_nothrow_assign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_nothrow_constructor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_nothrow_copy.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_plus.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_plus_assign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_right_shift.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_assign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_constructor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_copy.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_destructor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_move_assign.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\has_trivial_move_constructor.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\integral_constant.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\integral_promotion.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\intrinsics.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_abstract.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_arithmetic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_assignable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_base_and_derived.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_base_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_class.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_const.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_constructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_convertible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_copy_constructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_default_constructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_destructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_empty.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_enum.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_float.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_floating_point.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_function.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_fundamental.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_integral.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_lvalue_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_member_function_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_member_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_nothrow_move_assignable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_nothrow_move_constructible.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_pod.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_polymorphic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_rvalue_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_same.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_scalar.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_signed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_stateless.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_union.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_unsigned.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_void.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\is_volatile.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\make_signed.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\make_unsigned.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_all_extents.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_bounds.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_const.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_cv.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_extent.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_reference.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\remove_volatile.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\type_identity.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_traits\type_with_alignment.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\addressof.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\base_from_member.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\binary.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\compare_pointees.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\declval.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\detail\result_of_iterate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\enable_if.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\identity_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\result_of.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility\swap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\detail\config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\detail\uuid_generic.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\detail\uuid_x86.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\random_generator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\seed_rng.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\sha1.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="uuid\uuid.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="smart_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="static_assert.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="swap.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="throw_exception.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="token_functions.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="token_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="tokenizer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="type_index.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="utility.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="version.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="visit_each.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="weak_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="call_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="cerrno.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="checked_delete.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="concept_check.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="config.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="cstdint.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="current_function.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="enable_shared_from_this.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="exception_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="filesystem.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function_equal.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="function_output_iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="get_pointer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="integer_traits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="intrusive_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="io_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="is_placeholder.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="iterator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="lexical_cast.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="limits.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="make_shared.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math_fwd.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="mem_fn.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="memory_order.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="next_prior.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="non_type.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="noncopyable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="none.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="none_t.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="operators.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="optional.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="parameter.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="predef.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="rational.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ref.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="scoped_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="scoped_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="shared_array.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="shared_ptr.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="signals2.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libs\date_time\src\gregorian\greg_names.hpp">
      <Filter>源文件</Filter>
    </ClInclude>
    <ClInclude Include="libs\filesystem\src\windows_file_codecvt.hpp">
      <Filter>源文件</Filter>
    </ClInclude>
    <ClInclude Include="date_time\c_time.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="libs\atomic\src\lockpool.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\date_time\src\gregorian\date_generators.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\date_time\src\gregorian\gregorian_types.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\date_time\src\gregorian\greg_month.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\date_time\src\gregorian\greg_weekday.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\date_time\src\posix_time\posix_time_types.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\exception\src\clone_current_exception_non_intrusive.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\codecvt_error_category.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\operations.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\path.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\path_traits.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\portability.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\unique_path.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\utf8_codecvt_facet.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\filesystem\src\windows_file_codecvt.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\iostreams\src\file_descriptor.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\smart_ptr\src\sp_collector.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\smart_ptr\src\sp_debug_hooks.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\system\src\error_code.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\thread\src\future.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\thread\src\win32\thread.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\thread\src\win32\tss_dll.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\thread\src\win32\tss_pe.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libs\iostreams\src\mapped_file.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>