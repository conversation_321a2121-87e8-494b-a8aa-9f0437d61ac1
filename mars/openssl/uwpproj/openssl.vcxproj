<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5cf1ac8a-2274-41af-8979-3ef6f0b8d04b}</ProjectGuid>
    <Keyword>StaticLibrary</Keyword>
    <ProjectName>openssl</ProjectName>
    <RootNamespace>openssl</RootNamespace>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>14.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <WindowsTargetPlatformVersion>10.0.10240.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.10240.0</WindowsTargetPlatformMinVersion>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|arm'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|arm'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;UWP;PLATFORM_WP8;WP8;WP_PLATFORM;WIN32;WIN32_PLATFORM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\;..\crypto\evp;..\crypto\asn1;..\include\openssl;..\crypto;..\export_include;..\include\;$(ProjectDir);$(GeneratedFilesDir);$(IntDir);..\..\comm\UWP\;..\..\comm\windows;..\..\comm;..\UWP\zlib;..\..\;..\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>projdef.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\crypto\aes\aes.h" />
    <ClInclude Include="..\crypto\aes\aes_locl.h" />
    <ClInclude Include="..\crypto\asn1\asn1.h" />
    <ClInclude Include="..\crypto\asn1\asn1t.h" />
    <ClInclude Include="..\crypto\asn1\asn1_locl.h" />
    <ClInclude Include="..\crypto\asn1\asn1_mac.h" />
    <ClInclude Include="..\crypto\asn1\charmap.h" />
    <ClInclude Include="..\crypto\bio\bio.h" />
    <ClInclude Include="..\crypto\bio\bio_lcl.h" />
    <ClInclude Include="..\crypto\bn\bn.h" />
    <ClInclude Include="..\crypto\bn\bn_lcl.h" />
    <ClInclude Include="..\crypto\bn\bn_prime.h" />
    <ClInclude Include="..\crypto\buffer\buffer.h" />
    <ClInclude Include="..\crypto\conf\conf.h" />
    <ClInclude Include="..\crypto\constant_time_locl.h" />
    <ClInclude Include="..\crypto\cryptlib.h" />
    <ClInclude Include="..\crypto\crypto.h" />
    <ClInclude Include="..\crypto\ecdh\ecdh.h" />
    <ClInclude Include="..\crypto\ecdh\ech_locl.h" />
    <ClInclude Include="..\crypto\ecdsa\ecdsa.h" />
    <ClInclude Include="..\crypto\ecdsa\ecs_locl.h" />
    <ClInclude Include="..\crypto\ec\ec.h" />
    <ClInclude Include="..\crypto\ec\ec_lcl.h" />
    <ClInclude Include="..\crypto\err\err.h" />
    <ClInclude Include="..\crypto\evp\evp.h" />
    <ClInclude Include="..\crypto\evp\evp_locl.h" />
    <ClInclude Include="..\crypto\hmac\hmac.h" />
    <ClInclude Include="..\crypto\lhash\lhash.h" />
    <ClInclude Include="..\crypto\md32_common.h" />
    <ClInclude Include="..\crypto\md5\md5.h" />
    <ClInclude Include="..\crypto\md5\md5_locl.h" />
    <ClInclude Include="..\crypto\modes\modes.h" />
    <ClInclude Include="..\crypto\modes\modes_lcl.h" />
    <ClInclude Include="..\crypto\objects\objects.h" />
    <ClInclude Include="..\crypto\objects\obj_dat.h" />
    <ClInclude Include="..\crypto\objects\obj_mac.h" />
    <ClInclude Include="..\crypto\objects\obj_xref.h" />
    <ClInclude Include="..\crypto\opensslconf.h" />
    <ClInclude Include="..\crypto\opensslv.h" />
    <ClInclude Include="..\crypto\ossl_typ.h" />
    <ClInclude Include="..\crypto\o_str.h" />
    <ClInclude Include="..\crypto\o_time.h" />
    <ClInclude Include="..\crypto\pem\pem.h" />
    <ClInclude Include="..\crypto\pem\pem2.h" />
    <ClInclude Include="..\crypto\pkcs12\pkcs12.h" />
    <ClInclude Include="..\crypto\pkcs7\pkcs7.h" />
    <ClInclude Include="..\crypto\rand\rand.h" />
    <ClInclude Include="..\crypto\rand\rand_lcl.h" />
    <ClInclude Include="..\crypto\rsa\rsa.h" />
    <ClInclude Include="..\crypto\rsa\rsa_locl.h" />
    <ClInclude Include="..\crypto\sha\sha.h" />
    <ClInclude Include="..\crypto\sha\sha_locl.h" />
    <ClInclude Include="..\crypto\stack\safestack.h" />
    <ClInclude Include="..\crypto\stack\stack.h" />
    <ClInclude Include="..\crypto\symhacks.h" />
    <ClInclude Include="..\crypto\ui\ui.h" />
    <ClInclude Include="..\crypto\ui\ui_locl.h" />
    <ClInclude Include="..\crypto\x509v3\x509v3.h" />
    <ClInclude Include="..\crypto\x509\x509.h" />
    <ClInclude Include="..\crypto\x509\x509_vfy.h" />
    <ClInclude Include="..\export\crypto\rsa_pss_sha256.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\crypto\aes\aes_cbc.c" />
    <ClCompile Include="..\crypto\aes\aes_cfb.c" />
    <ClCompile Include="..\crypto\aes\aes_core.c" />
    <ClCompile Include="..\crypto\aes\aes_ctr.c" />
    <ClCompile Include="..\crypto\aes\aes_ecb.c" />
    <ClCompile Include="..\crypto\aes\aes_ige.c" />
    <ClCompile Include="..\crypto\aes\aes_misc.c" />
    <ClCompile Include="..\crypto\aes\aes_ofb.c" />
    <ClCompile Include="..\crypto\aes\aes_wrap.c" />
    <ClCompile Include="..\crypto\aes\aes_x86core.c" />
    <ClCompile Include="..\crypto\asn1\ameth_lib.c" />
    <ClCompile Include="..\crypto\asn1\asn1_err.c" />
    <ClCompile Include="..\crypto\asn1\asn1_lib.c" />
    <ClCompile Include="..\crypto\asn1\asn1_par.c" />
    <ClCompile Include="..\crypto\asn1\asn_pack.c" />
    <ClCompile Include="..\crypto\asn1\a_bitstr.c" />
    <ClCompile Include="..\crypto\asn1\a_bool.c" />
    <ClCompile Include="..\crypto\asn1\a_bytes.c" />
    <ClCompile Include="..\crypto\asn1\a_d2i_fp.c" />
    <ClCompile Include="..\crypto\asn1\a_dup.c" />
    <ClCompile Include="..\crypto\asn1\a_enum.c" />
    <ClCompile Include="..\crypto\asn1\a_gentm.c" />
    <ClCompile Include="..\crypto\asn1\a_i2d_fp.c" />
    <ClCompile Include="..\crypto\asn1\a_int.c" />
    <ClCompile Include="..\crypto\asn1\a_mbstr.c" />
    <ClCompile Include="..\crypto\asn1\a_object.c" />
    <ClCompile Include="..\crypto\asn1\a_octet.c" />
    <ClCompile Include="..\crypto\asn1\a_print.c" />
    <ClCompile Include="..\crypto\asn1\a_set.c" />
    <ClCompile Include="..\crypto\asn1\a_strnid.c" />
    <ClCompile Include="..\crypto\asn1\a_time.c" />
    <ClCompile Include="..\crypto\asn1\a_type.c" />
    <ClCompile Include="..\crypto\asn1\a_utctm.c" />
    <ClCompile Include="..\crypto\asn1\a_utf8.c" />
    <ClCompile Include="..\crypto\asn1\bio_asn1.c" />
    <ClCompile Include="..\crypto\asn1\bio_ndef.c" />
    <ClCompile Include="..\crypto\asn1\d2i_pr.c" />
    <ClCompile Include="..\crypto\asn1\evp_asn1.c" />
    <ClCompile Include="..\crypto\asn1\f_enum.c" />
    <ClCompile Include="..\crypto\asn1\f_int.c" />
    <ClCompile Include="..\crypto\asn1\f_string.c" />
    <ClCompile Include="..\crypto\asn1\p8_pkey.c" />
    <ClCompile Include="..\crypto\asn1\tasn_dec.c" />
    <ClCompile Include="..\crypto\asn1\tasn_enc.c" />
    <ClCompile Include="..\crypto\asn1\tasn_fre.c" />
    <ClCompile Include="..\crypto\asn1\tasn_new.c" />
    <ClCompile Include="..\crypto\asn1\tasn_typ.c" />
    <ClCompile Include="..\crypto\asn1\tasn_utl.c" />
    <ClCompile Include="..\crypto\asn1\t_pkey.c" />
    <ClCompile Include="..\crypto\asn1\x_algor.c" />
    <ClCompile Include="..\crypto\asn1\x_attrib.c" />
    <ClCompile Include="..\crypto\asn1\x_bignum.c" />
    <ClCompile Include="..\crypto\asn1\x_long.c" />
    <ClCompile Include="..\crypto\asn1\x_pubkey.c" />
    <ClCompile Include="..\crypto\asn1\x_sig.c" />
    <ClCompile Include="..\crypto\bio\bf_buff.c" />
    <ClCompile Include="..\crypto\bio\bf_lbuf.c" />
    <ClCompile Include="..\crypto\bio\bf_nbio.c" />
    <ClCompile Include="..\crypto\bio\bf_null.c" />
    <ClCompile Include="..\crypto\bio\bio_cb.c" />
    <ClCompile Include="..\crypto\bio\bio_err.c" />
    <ClCompile Include="..\crypto\bio\bio_lib.c" />
    <ClCompile Include="..\crypto\bio\bss_bio.c" />
    <ClCompile Include="..\crypto\bio\bss_fd.c" />
    <ClCompile Include="..\crypto\bio\bss_file.c" />
    <ClCompile Include="..\crypto\bio\bss_log.c" />
    <ClCompile Include="..\crypto\bio\bss_mem.c" />
    <ClCompile Include="..\crypto\bio\bss_null.c" />
    <ClCompile Include="..\crypto\bio\b_dump.c" />
    <ClCompile Include="..\crypto\bio\b_print.c" />
    <ClCompile Include="..\crypto\bn\bn_add.c" />
    <ClCompile Include="..\crypto\bn\bn_asm.c" />
    <ClCompile Include="..\crypto\bn\bn_blind.c" />
    <ClCompile Include="..\crypto\bn\bn_const.c" />
    <ClCompile Include="..\crypto\bn\bn_ctx.c" />
    <ClCompile Include="..\crypto\bn\bn_depr.c" />
    <ClCompile Include="..\crypto\bn\bn_div.c" />
    <ClCompile Include="..\crypto\bn\bn_err.c" />
    <ClCompile Include="..\crypto\bn\bn_exp.c" />
    <ClCompile Include="..\crypto\bn\bn_exp2.c" />
    <ClCompile Include="..\crypto\bn\bn_gcd.c" />
    <ClCompile Include="..\crypto\bn\bn_gf2m.c" />
    <ClCompile Include="..\crypto\bn\bn_kron.c" />
    <ClCompile Include="..\crypto\bn\bn_lib.c" />
    <ClCompile Include="..\crypto\bn\bn_mod.c" />
    <ClCompile Include="..\crypto\bn\bn_mont.c" />
    <ClCompile Include="..\crypto\bn\bn_mpi.c" />
    <ClCompile Include="..\crypto\bn\bn_mul.c" />
    <ClCompile Include="..\crypto\bn\bn_nist.c" />
    <ClCompile Include="..\crypto\bn\bn_prime.c" />
    <ClCompile Include="..\crypto\bn\bn_print.c" />
    <ClCompile Include="..\crypto\bn\bn_rand.c" />
    <ClCompile Include="..\crypto\bn\bn_recp.c" />
    <ClCompile Include="..\crypto\bn\bn_shift.c" />
    <ClCompile Include="..\crypto\bn\bn_sqr.c" />
    <ClCompile Include="..\crypto\bn\bn_sqrt.c" />
    <ClCompile Include="..\crypto\bn\bn_word.c" />
    <ClCompile Include="..\crypto\bn\vms-helper.c" />
    <ClCompile Include="..\crypto\buffer\buffer.c" />
    <ClCompile Include="..\crypto\buffer\buf_err.c" />
    <ClCompile Include="..\crypto\cpt_err.c" />
    <ClCompile Include="..\crypto\cryptlib.c" />
    <ClCompile Include="..\crypto\cversion.c" />
    <ClCompile Include="..\crypto\ecdh\ech_err.c" />
    <ClCompile Include="..\crypto\ecdh\ech_key.c" />
    <ClCompile Include="..\crypto\ecdh\ech_lib.c" />
    <ClCompile Include="..\crypto\ecdh\ech_ossl.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_asn1.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_err.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_lib.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_ossl.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_sign.c" />
    <ClCompile Include="..\crypto\ecdsa\ecs_vrf.c" />
    <ClCompile Include="..\crypto\ec\ec2_mult.c" />
    <ClCompile Include="..\crypto\ec\ec2_smpl.c" />
    <ClCompile Include="..\crypto\ec\ecp_mont.c" />
    <ClCompile Include="..\crypto\ec\ecp_nist.c" />
    <ClCompile Include="..\crypto\ec\ecp_smpl.c" />
    <ClCompile Include="..\crypto\ec\ec_ameth.c" />
    <ClCompile Include="..\crypto\ec\ec_asn1.c" />
    <ClCompile Include="..\crypto\ec\ec_check.c" />
    <ClCompile Include="..\crypto\ec\ec_curve.c" />
    <ClCompile Include="..\crypto\ec\ec_cvt.c" />
    <ClCompile Include="..\crypto\ec\ec_err.c" />
    <ClCompile Include="..\crypto\ec\ec_key.c" />
    <ClCompile Include="..\crypto\ec\ec_lib.c" />
    <ClCompile Include="..\crypto\ec\ec_mult.c" />
    <ClCompile Include="..\crypto\ec\ec_pmeth.c" />
    <ClCompile Include="..\crypto\ec\ec_print.c" />
    <ClCompile Include="..\crypto\err\err.c" />
    <ClCompile Include="..\crypto\evp\bio_b64.c" />
    <ClCompile Include="..\crypto\evp\bio_enc.c" />
    <ClCompile Include="..\crypto\evp\bio_md.c" />
    <ClCompile Include="..\crypto\evp\bio_ok.c" />
    <ClCompile Include="..\crypto\evp\c_all.c" />
    <ClCompile Include="..\crypto\evp\c_allc.c" />
    <ClCompile Include="..\crypto\evp\c_alld.c" />
    <ClCompile Include="..\crypto\evp\digest.c" />
    <ClCompile Include="..\crypto\evp\encode.c" />
    <ClCompile Include="..\crypto\evp\evp_acnf.c" />
    <ClCompile Include="..\crypto\evp\evp_enc.c" />
    <ClCompile Include="..\crypto\evp\evp_err.c" />
    <ClCompile Include="..\crypto\evp\evp_fips.c" />
    <ClCompile Include="..\crypto\evp\evp_key.c" />
    <ClCompile Include="..\crypto\evp\evp_lib.c" />
    <ClCompile Include="..\crypto\evp\evp_pbe.c" />
    <ClCompile Include="..\crypto\evp\evp_pkey.c" />
    <ClCompile Include="..\crypto\evp\e_aes.c" />
    <ClCompile Include="..\crypto\evp\e_aes_cbc_hmac_sha1.c" />
    <ClCompile Include="..\crypto\evp\e_bf.c" />
    <ClCompile Include="..\crypto\evp\e_camellia.c" />
    <ClCompile Include="..\crypto\evp\e_cast.c" />
    <ClCompile Include="..\crypto\evp\e_idea.c" />
    <ClCompile Include="..\crypto\evp\e_null.c" />
    <ClCompile Include="..\crypto\evp\e_old.c" />
    <ClCompile Include="..\crypto\evp\e_rc2.c" />
    <ClCompile Include="..\crypto\evp\e_rc4.c" />
    <ClCompile Include="..\crypto\evp\e_rc4_hmac_md5.c" />
    <ClCompile Include="..\crypto\evp\e_rc5.c" />
    <ClCompile Include="..\crypto\evp\e_seed.c" />
    <ClCompile Include="..\crypto\evp\m_dss.c" />
    <ClCompile Include="..\crypto\evp\m_dss1.c" />
    <ClCompile Include="..\crypto\evp\m_ecdsa.c" />
    <ClCompile Include="..\crypto\evp\m_md2.c" />
    <ClCompile Include="..\crypto\evp\m_md4.c" />
    <ClCompile Include="..\crypto\evp\m_md5.c" />
    <ClCompile Include="..\crypto\evp\m_mdc2.c" />
    <ClCompile Include="..\crypto\evp\m_null.c" />
    <ClCompile Include="..\crypto\evp\m_ripemd.c" />
    <ClCompile Include="..\crypto\evp\m_sha.c" />
    <ClCompile Include="..\crypto\evp\m_sha1.c" />
    <ClCompile Include="..\crypto\evp\m_sigver.c" />
    <ClCompile Include="..\crypto\evp\m_wp.c" />
    <ClCompile Include="..\crypto\evp\names.c" />
    <ClCompile Include="..\crypto\evp\openbsd_hw.c" />
    <ClCompile Include="..\crypto\evp\p5_crpt.c" />
    <ClCompile Include="..\crypto\evp\p5_crpt2.c" />
    <ClCompile Include="..\crypto\evp\pmeth_fn.c" />
    <ClCompile Include="..\crypto\evp\pmeth_gn.c" />
    <ClCompile Include="..\crypto\evp\pmeth_lib.c" />
    <ClCompile Include="..\crypto\evp\p_dec.c" />
    <ClCompile Include="..\crypto\evp\p_enc.c" />
    <ClCompile Include="..\crypto\evp\p_lib.c" />
    <ClCompile Include="..\crypto\evp\p_open.c" />
    <ClCompile Include="..\crypto\evp\p_seal.c" />
    <ClCompile Include="..\crypto\evp\p_sign.c" />
    <ClCompile Include="..\crypto\evp\p_verify.c" />
    <ClCompile Include="..\crypto\ex_data.c" />
    <ClCompile Include="..\crypto\hmac\hmac.c" />
    <ClCompile Include="..\crypto\hmac\hmactest.c" />
    <ClCompile Include="..\crypto\hmac\hm_ameth.c" />
    <ClCompile Include="..\crypto\hmac\hm_pmeth.c" />
    <ClCompile Include="..\crypto\lhash\lhash.c" />
    <ClCompile Include="..\crypto\lhash\lh_stats.c" />
    <ClCompile Include="..\crypto\md5\md5_dgst.c" />
    <ClCompile Include="..\crypto\md5\md5_one.c" />
    <ClCompile Include="..\crypto\mem.c" />
    <ClCompile Include="..\crypto\mem_clr.c" />
    <ClCompile Include="..\crypto\mem_dbg.c" />
    <ClCompile Include="..\crypto\modes\cbc128.c" />
    <ClCompile Include="..\crypto\modes\ccm128.c" />
    <ClCompile Include="..\crypto\modes\cfb128.c" />
    <ClCompile Include="..\crypto\modes\ctr128.c" />
    <ClCompile Include="..\crypto\modes\cts128.c" />
    <ClCompile Include="..\crypto\modes\gcm128.c" />
    <ClCompile Include="..\crypto\modes\ofb128.c" />
    <ClCompile Include="..\crypto\modes\xts128.c" />
    <ClCompile Include="..\crypto\objects\obj_dat.c" />
    <ClCompile Include="..\crypto\objects\obj_err.c" />
    <ClCompile Include="..\crypto\objects\obj_lib.c" />
    <ClCompile Include="..\crypto\objects\obj_xref.c" />
    <ClCompile Include="..\crypto\objects\o_names.c" />
    <ClCompile Include="..\crypto\o_init.c" />
    <ClCompile Include="..\crypto\o_str.c" />
    <ClCompile Include="..\crypto\o_time.c" />
    <ClCompile Include="..\crypto\pem\pem_all.c" />
    <ClCompile Include="..\crypto\pem\pem_lib.c" />
    <ClCompile Include="..\crypto\pem\pem_oth.c" />
    <ClCompile Include="..\crypto\pem\pem_pkey.c" />
    <ClCompile Include="..\crypto\pkcs12\p12_decr.c" />
    <ClCompile Include="..\crypto\pkcs12\p12_p8d.c" />
    <ClCompile Include="..\crypto\rand\md_rand.c" />
    <ClCompile Include="..\crypto\rand\randfile.c" />
    <ClCompile Include="..\crypto\rand\rand_egd.c" />
    <ClCompile Include="..\crypto\rand\rand_err.c" />
    <ClCompile Include="..\crypto\rand\rand_lib.c" />
    <ClCompile Include="..\crypto\rand\rand_win.c" />
    <ClCompile Include="..\crypto\rsa\rsa_ameth.c" />
    <ClCompile Include="..\crypto\rsa\rsa_asn1.c" />
    <ClCompile Include="..\crypto\rsa\rsa_chk.c" />
    <ClCompile Include="..\crypto\rsa\rsa_depr.c" />
    <ClCompile Include="..\crypto\rsa\rsa_eay.c" />
    <ClCompile Include="..\crypto\rsa\rsa_err.c" />
    <ClCompile Include="..\crypto\rsa\rsa_gen.c" />
    <ClCompile Include="..\crypto\rsa\rsa_lib.c" />
    <ClCompile Include="..\crypto\rsa\rsa_none.c" />
    <ClCompile Include="..\crypto\rsa\rsa_null.c" />
    <ClCompile Include="..\crypto\rsa\rsa_oaep.c" />
    <ClCompile Include="..\crypto\rsa\rsa_pk1.c" />
    <ClCompile Include="..\crypto\rsa\rsa_pmeth.c" />
    <ClCompile Include="..\crypto\rsa\rsa_prn.c" />
    <ClCompile Include="..\crypto\rsa\rsa_pss.c" />
    <ClCompile Include="..\crypto\rsa\rsa_saos.c" />
    <ClCompile Include="..\crypto\rsa\rsa_sign.c" />
    <ClCompile Include="..\crypto\rsa\rsa_ssl.c" />
    <ClCompile Include="..\crypto\rsa\rsa_x931.c" />
    <ClCompile Include="..\crypto\sha\sha1dgst.c" />
    <ClCompile Include="..\crypto\sha\sha1_one.c" />
    <ClCompile Include="..\crypto\sha\sha256.c" />
    <ClCompile Include="..\crypto\sha\sha512.c" />
    <ClCompile Include="..\crypto\sha\sha_dgst.c" />
    <ClCompile Include="..\crypto\stack\stack.c" />
    <ClCompile Include="..\crypto\uid.c" />
    <ClCompile Include="..\crypto\x509v3\v3_utl.c" />
    <ClCompile Include="..\crypto\x509\x509_att.c" />
    <ClCompile Include="..\export\aes_crypt.c" />
    <ClCompile Include="..\export\crypto\gen_rsa_key_pair.cpp" />
    <ClCompile Include="..\export\crypto\iCoreCrypt.cpp" />
    <ClCompile Include="..\export\crypto\openssl_multi_thread_support.cpp" />
    <ClCompile Include="..\export\crypto\pay_openssl_crypto_util.cpp" />
    <ClCompile Include="..\export\crypto\rsa_crypt.cpp" />
    <ClCompile Include="..\export\crypto\rsa_pss_sha256.cpp" />
    <ClCompile Include="..\export\crypto\test_rsa_pss_sha256.cpp" />
    <ClCompile Include="..\export\ecdh_crypt.c" />
    <ClCompile Include="..\export\ecdsa_verify.c" />
    <ClCompile Include="..\export\md5_digest.c" />
    <ClCompile Include="..\export\rsa_private_decrypt.c" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>