group = PROJ_GROUP
version = PROJ_VERSION
project.archivesBaseName = PROJ_ARTIFACTID

apply plugin: 'com.jfrog.bintray'
apply plugin: "com.jfrog.artifactory"
apply plugin: 'maven-publish'

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.sourceFiles
    classifier = 'sources'
}

task javadoc(type: Javadoc) {
    options.charSet = 'UTF-8'
    options.encoding = "UTF-8"
    source = android.sourceSets.main.java.srcDirs
    classpath += configurations.compile
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
}

task javadocJar(type: Jar, dependsOn: javadoc) {
    classifier = 'javadoc'
    from javadoc.destinationDir
}

task androidJniSymbolsJar(type: Jar) {
    classifier = 'symbols'
    from file('obj/local/')
    include '**/*.so'
}

javadoc {
    options{
        encoding "UTF-8"
        charSet 'UTF-8'
        author true
        version true
        links "http://docs.oracle.com/javase/7/docs/api"
        title "$PROJ_NAME $PROJ_VERSION"
    }
}


def pomConfig = {
    licenses {
        license {
            name "MIT License"
            url "http://opensource.org/licenses/MIT"
            distribution "repo"
        }
    }
    developers {
        developer {
            id DEVELOPER_ID
            name DEVELOPER_NAME
            email DEVELOPER_EMAIL
        }
    }
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            artifactId PROJ_ARTIFACTID
            artifact javadocJar
            artifact sourcesJar
            artifact androidJniSymbolsJar

            pom.withXml {
                def root = asNode()
                root.appendNode('description', PROJ_DESCRIPTION)
                root.children().last() + pomConfig

                def dependenciesNode = root.appendNode('dependencies')
                configurations.compile.allDependencies.each {
                    if (it.group && it.name && it.version) {
                        def dependencyNode = dependenciesNode.appendNode('dependency')
                        dependencyNode.appendNode('groupId', it.group)
                        dependencyNode.appendNode('artifactId', it.name)
                        dependencyNode.appendNode('version', it.version)
                    }
                }
            }
        }
    }
}

afterEvaluate {
    publishing.publications.mavenJava.artifact(bundleRelease)
}

bintray {
    Properties properties = new Properties()
    if (rootProject.file("local.properties").exists()) {
        properties.load(project.rootProject.file('local.properties').newDataInputStream())
        user = properties.getProperty("bintray.user")
        key = properties.getProperty("bintray.apikey")
    }

    publications = ['mavenJava']
    publish = true

    pkg {
//        repo = 'maven'
//        userOrg = 'marsopen'
        repo = 'mars'
        userOrg= 'weixin210'
        name = PROJ_NAME
        desc = PROJ_DESCRIPTION
        websiteUrl = PROJ_WEBSITEURL
        issueTrackerUrl = PROJ_ISSUETRACKERURL
        vcsUrl = PROJ_VCSURL
        licenses = ['MIT']
        publicDownloadNumbers = true
    }
}

artifactory {
    contextUrl = 'http://oss.jfrog.org/artifactory'
    resolve {
        repository {
            repoKey = 'libs-release'
        }
    }
    publish {
        repository {
            repoKey = 'oss-snapshot-local' //The Artifactory repository key to publish to
            username = bintray.user
            password = bintray.key
        }
        defaults {
            //这里的名字和上面红色的名字一致即可，会将其包含的输出上传到jfrog上去
            publications('mavenJava')
            publishArtifacts = true
        }
    }
}