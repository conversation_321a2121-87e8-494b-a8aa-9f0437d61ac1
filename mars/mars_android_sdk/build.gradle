apply plugin: "com.android.library"

android {
    compileSdkVersion 23
    buildToolsVersion '23.0.3'
    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 21
        ndk {
            // TODO: changes this for your application if needed
            moduleName = "mmnet"
            abiFilter "armeabi"
            abiFilter "armeabi-v7a"
        }
    }
    sourceSets {
        main {
            java {
                srcDir "src/main/java"
            }
            jni {
                srcDir "src/main/jni"
            }
            jniLibs {
                srcDir "libs"
            }
        }
    }
    buildTypes {
        release {
            proguardFile 'proguard-rules.pro'
        }
    }
    ndkVersion '25.1.8937393'
}


dependencies {
//    compile 'javax.validation:validation-api:1.1.0.Final'
}

//apply from: "${rootDir}/gradle/build_libraries.gradle"
//apply from: 'bintray.gradle'