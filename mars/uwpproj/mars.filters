<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Resources">
      <UniqueIdentifier>59083037-6b38-488d-8aa6-7317ebe642b2</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tga;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\baseevent\src\active_logic.cc" />
    <ClCompile Include="..\baseevent\src\baseprj.cc" />
    <ClCompile Include="..\baseevent\src\baseprjevent.cc" />
    <ClCompile Include="..\boost\libs\atomic\src\lockpool.cpp" />
    <ClCompile Include="..\boost\libs\context\src\posix\stack_traits.cpp" />
    <ClCompile Include="..\boost\libs\context\src\windows\stack_traits.cpp" />
    <ClCompile Include="..\boost\libs\context\src\execution_context.cpp" />
    <ClCompile Include="..\boost\libs\coroutine\src\detail\coroutine_context.cpp" />
    <ClCompile Include="..\boost\libs\coroutine\src\posix\stack_traits.cpp" />
    <ClCompile Include="..\boost\libs\coroutine\src\windows\stack_traits.cpp" />
    <ClCompile Include="..\boost\libs\coroutine\src\exceptions.cpp" />
    <ClCompile Include="..\boost\libs\date_time\src\gregorian\date_generators.cpp" />
    <ClCompile Include="..\boost\libs\date_time\src\gregorian\greg_month.cpp" />
    <ClCompile Include="..\boost\libs\date_time\src\gregorian\greg_weekday.cpp" />
    <ClCompile Include="..\boost\libs\date_time\src\gregorian\gregorian_types.cpp" />
    <ClCompile Include="..\boost\libs\date_time\src\posix_time\posix_time_types.cpp" />
    <ClCompile Include="..\boost\libs\exception\src\clone_current_exception_non_intrusive.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\codecvt_error_category.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\operations.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\path.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\path_traits.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\portability.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\unique_path.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\utf8_codecvt_facet.cpp" />
    <ClCompile Include="..\boost\libs\filesystem\src\windows_file_codecvt.cpp" />
    <ClCompile Include="..\boost\libs\iostreams\src\file_descriptor.cpp" />
    <ClCompile Include="..\boost\libs\iostreams\src\mapped_file.cpp" />
    <ClCompile Include="..\boost\libs\smart_ptr\src\sp_collector.cpp" />
    <ClCompile Include="..\boost\libs\smart_ptr\src\sp_debug_hooks.cpp" />
    <ClCompile Include="..\boost\libs\system\src\error_code.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\pthread\once.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\pthread\once_atomic.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\pthread\thread.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\win32\thread.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\win32\tss_dll.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\win32\tss_pe.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\future.cpp" />
    <ClCompile Include="..\boost\libs\thread\src\tss_null.cpp" />
    <ClCompile Include="..\comm\android\callstack.cc" />
    <ClCompile Include="..\comm\android\dumpcrash_stack.cc" />
    <ClCompile Include="..\comm\android\getprocessname.c" />
    <ClCompile Include="..\comm\android\wakeuplock.cc" />
    <ClCompile Include="..\comm\assert\__assert.c" />
    <ClCompile Include="..\comm\corepattern\coreservice_base.cc" />
    <ClCompile Include="..\comm\coroutine\coro_socket.cc" />
    <ClCompile Include="..\comm\crypt\ibase64.cc" />
    <ClCompile Include="..\comm\crypt\pkcs7_padding.c" />
    <ClCompile Include="..\comm\dns\dns.cc" />
    <ClCompile Include="..\comm\messagequeue\message_queue.cc" />
    <ClCompile Include="..\comm\messagequeue\message_queue_utils.cc" />
    <ClCompile Include="..\comm\network\getdnssvraddrs.cc" />
    <ClCompile Include="..\comm\network\getgateway.c" />
    <ClCompile Include="..\comm\network\getifaddrs.cc" />
    <ClCompile Include="..\comm\network\netinfo_util.cc" />
    <ClCompile Include="..\comm\socket\block_socket.cc" />
    <ClCompile Include="..\comm\socket\complexconnect.cc" />
    <ClCompile Include="..\comm\socket\getsocktcpinfo.cc" />
    <ClCompile Include="..\comm\socket\local_ipstack.cc" />
    <ClCompile Include="..\comm\socket\nat64_prefix_util.cc" />
    <ClCompile Include="..\comm\socket\socket_address.cc" />
    <ClCompile Include="..\comm\socket\tcpclient.cc" />
    <ClCompile Include="..\comm\socket\tcpclient_fsm.cc" />
    <ClCompile Include="..\comm\socket\tcpserver.cc" />
    <ClCompile Include="..\comm\socket\tcpserver_fsm.cc" />
    <ClCompile Include="..\comm\socket\udpclient.cc" />
    <ClCompile Include="..\comm\socket\udpserver.cc" />
    <ClCompile Include="..\comm\socket\unix_socket.cc" />
    <ClCompile Include="..\log\crypt\log_crypt.cc" />
    <ClCompile Include="..\log\src\appender.cc" />
    <ClCompile Include="..\log\src\formater.cc" />
    <ClCompile Include="..\log\src\log_buffer.cc" />
    <ClCompile Include="..\sdt\src\activecheck\basechecker.cc" />
    <ClCompile Include="..\sdt\src\activecheck\dnschecker.cc" />
    <ClCompile Include="..\sdt\src\activecheck\httpchecker.cc" />
    <ClCompile Include="..\sdt\src\activecheck\pingchecker.cc" />
    <ClCompile Include="..\sdt\src\activecheck\tcpchecker.cc" />
    <ClCompile Include="..\sdt\src\checkimpl\dnsquery.cc" />
    <ClCompile Include="..\sdt\src\checkimpl\httpquery.cc" />
    <ClCompile Include="..\sdt\src\checkimpl\pingquery.cc" />
    <ClCompile Include="..\sdt\src\checkimpl\tcpquery.cc" />
    <ClCompile Include="..\sdt\src\tools\netchecker_trafficmonitor.cc" />
    <ClCompile Include="..\sdt\src\sdt_core.cc" />
    <ClCompile Include="..\stn\src\anti_avalanche.cc" />
    <ClCompile Include="..\stn\src\dynamic_timeout.cc" />
    <ClCompile Include="..\stn\src\flow_limit.cc" />
    <ClCompile Include="..\stn\src\frequency_limit.cc" />
    <ClCompile Include="..\stn\src\longlink.cc" />
    <ClCompile Include="..\stn\src\longlink_connect_monitor.cc" />
    <ClCompile Include="..\stn\src\longlink_identify_checker.cc" />
    <ClCompile Include="..\stn\src\longlink_speed_test.cc" />
    <ClCompile Include="..\stn\src\longlink_task_manager.cc" />
    <ClCompile Include="..\stn\src\net_channel_factory.cc" />
    <ClCompile Include="..\stn\src\net_check_logic.cc" />
    <ClCompile Include="..\stn\src\net_core.cc" />
    <ClCompile Include="..\stn\src\net_source.cc" />
    <ClCompile Include="..\stn\src\netsource_timercheck.cc" />
    <ClCompile Include="..\stn\src\shortlink.cc" />
    <ClCompile Include="..\stn\src\shortlink_task_manager.cc" />
    <ClCompile Include="..\stn\src\signalling_keeper.cc" />
    <ClCompile Include="..\stn\src\simple_ipport_sort.cc" />
    <ClCompile Include="..\stn\src\smart_heartbeat.cc" />
    <ClCompile Include="..\stn\src\task_profile.cc" />
    <ClCompile Include="..\stn\src\timing_sync.cc" />
    <ClCompile Include="..\stn\src\zombie_task_manager.cc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\boost\libs\date_time\src\gregorian\greg_names.hpp" />
    <ClInclude Include="..\boost\libs\filesystem\src\windows_file_codecvt.hpp" />
    <ClInclude Include="..\comm\socket\block_socket.h" />
    <ClInclude Include="..\comm\socket\complexconnect.h" />
    <ClInclude Include="..\comm\socket\getsocktcpinfo.h" />
    <ClInclude Include="..\comm\socket\ipv6_address_utils.h" />
    <ClInclude Include="..\comm\socket\local_ipstack.h" />
    <ClInclude Include="..\comm\socket\nat64_prefix_util.h" />
    <ClInclude Include="..\comm\socket\socket_address.h" />
    <ClInclude Include="..\comm\socket\socketselect.h" />
    <ClInclude Include="..\comm\socket\tcp_fsm_handler.h" />
    <ClInclude Include="..\comm\socket\tcpclient.h" />
    <ClInclude Include="..\comm\socket\tcpclient_fsm.h" />
    <ClInclude Include="..\comm\socket\tcpserver.h" />
    <ClInclude Include="..\comm\socket\tcpserver_fsm.h" />
    <ClInclude Include="..\comm\socket\udpclient.h" />
    <ClInclude Include="..\comm\socket\udpserver.h" />
    <ClInclude Include="..\comm\socket\unix_socket.h" />
    <ClInclude Include="..\comm\thread\atomic_oper.h" />
    <ClInclude Include="..\comm\thread\condition.h" />
    <ClInclude Include="..\comm\thread\lock.h" />
    <ClInclude Include="..\comm\thread\mutex.h" />
    <ClInclude Include="..\comm\thread\mutexvector.h" />
    <ClInclude Include="..\comm\thread\runnable.h" />
    <ClInclude Include="..\comm\thread\spinlock.h" />
    <ClInclude Include="..\comm\thread\thread.h" />
    <ClInclude Include="..\comm\thread\tss.h" />
    <ClInclude Include="..\log\crypt\log_crypt.h" />
    <ClInclude Include="..\log\src\log_buffer.h" />
    <ClInclude Include="..\sdt\src\activecheck\basechecker.h" />
    <ClInclude Include="..\sdt\src\activecheck\dnschecker.h" />
    <ClInclude Include="..\sdt\src\activecheck\httpchecker.h" />
    <ClInclude Include="..\sdt\src\activecheck\pingchecker.h" />
    <ClInclude Include="..\sdt\src\activecheck\tcpchecker.h" />
    <ClInclude Include="..\sdt\src\checkimpl\dnsquery.h" />
    <ClInclude Include="..\sdt\src\checkimpl\http_url_parser.h" />
    <ClInclude Include="..\sdt\src\checkimpl\httpquery.h" />
    <ClInclude Include="..\sdt\src\checkimpl\pingquery.h" />
    <ClInclude Include="..\sdt\src\checkimpl\tcpquery.h" />
    <ClInclude Include="..\sdt\src\tools\netchecker_socketutils.hpp" />
    <ClInclude Include="..\sdt\src\tools\netchecker_trafficmonitor.h" />
    <ClInclude Include="..\sdt\src\sdt_core.h" />
    <ClInclude Include="..\stn\src\anti_avalanche.h" />
    <ClInclude Include="..\stn\src\dynamic_timeout.h" />
    <ClInclude Include="..\stn\src\flow_limit.h" />
    <ClInclude Include="..\stn\src\frequency_limit.h" />
    <ClInclude Include="..\stn\src\longlink.h" />
    <ClInclude Include="..\stn\src\longlink_connect_monitor.h" />
    <ClInclude Include="..\stn\src\longlink_identify_checker.h" />
    <ClInclude Include="..\stn\src\longlink_speed_test.h" />
    <ClInclude Include="..\stn\src\longlink_task_manager.h" />
    <ClInclude Include="..\stn\src\net_channel_factory.h" />
    <ClInclude Include="..\stn\src\net_check_logic.h" />
    <ClInclude Include="..\stn\src\net_core.h" />
    <ClInclude Include="..\stn\src\net_source.h" />
    <ClInclude Include="..\stn\src\netsource_timercheck.h" />
    <ClInclude Include="..\stn\src\shortlink.h" />
    <ClInclude Include="..\stn\src\shortlink_interface.h" />
    <ClInclude Include="..\stn\src\shortlink_task_manager.h" />
    <ClInclude Include="..\stn\src\signalling_keeper.h" />
    <ClInclude Include="..\stn\src\simple_ipport_sort.h" />
    <ClInclude Include="..\stn\src\smart_heartbeat.h" />
    <ClInclude Include="..\stn\src\special_ini.h" />
    <ClInclude Include="..\stn\src\timing_sync.h" />
    <ClInclude Include="..\stn\src\zombie_task_manager.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\boost\libs\context\src\asm\jump_arm_aapcs_pe_armasm.asm" />
    <None Include="..\boost\libs\context\src\asm\jump_i386_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\jump_i386_ms_pe_masm.asm" />
    <None Include="..\boost\libs\context\src\asm\jump_x86_64_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\jump_x86_64_ms_pe_masm.asm" />
    <None Include="..\boost\libs\context\src\asm\make_arm_aapcs_pe_armasm.asm" />
    <None Include="..\boost\libs\context\src\asm\make_i386_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\make_i386_ms_pe_masm.asm" />
    <None Include="..\boost\libs\context\src\asm\make_x86_64_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\make_x86_64_ms_pe_masm.asm" />
    <None Include="..\boost\libs\context\src\asm\ontop_arm_aapcs_pe_armasm.asm" />
    <None Include="..\boost\libs\context\src\asm\ontop_i386_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\ontop_i386_ms_pe_masm.asm" />
    <None Include="..\boost\libs\context\src\asm\ontop_x86_64_ms_pe_gas.asm" />
    <None Include="..\boost\libs\context\src\asm\ontop_x86_64_ms_pe_masm.asm" />
  </ItemGroup>
</Project>