#!/usr/bin/env python3
"""
Logger模块重构脚本
用于批量重构剩余的日志模块类
"""

import re
import os

# 模块配置映射
MODULE_CONFIGS = {
    'ConstructionHighLog': {
        'module_name': 'DefaultModule.CONS_HIGH',
        'max_file_size': '1024 * 1024',  # 1MB
        'log_count': None,
        'should_flush': True,
        'comment': '工程机械高频日志模块'
    },
    'ConstructionCommonLog': {
        'module_name': 'DefaultModule.CONS_COMMON', 
        'max_file_size': '1024 * 1024',  # 1MB
        'log_count': None,
        'should_flush': False,
        'comment': '工程机械通用日志模块'
    },
    'BoardObserveLog': {
        'module_name': 'DefaultModule.BOARD_OBSERVER',
        'max_file_size': '10 * 1024 * 1024',  # 10MB
        'log_count': '20',
        'should_flush': False,
        'comment': '板卡观测量日志模块'
    },
    'IsoBusLog': {
        'module_name': 'DefaultModule.ISO_BUS',
        'max_file_size': '10 * 1024 * 1024',  # 10MB
        'log_count': '20',
        'should_flush': True,
        'comment': 'ISO总线日志模块'
    },
    'RtcmLog': {
        'module_name': 'DefaultModule.RTCM',
        'max_file_size': '10 * 1024 * 1024',  # 10MB
        'log_count': '20', 
        'should_flush': False,
        'comment': 'RTCM日志模块'
    }
}

def generate_refactored_class(class_name, config):
    """生成重构后的类代码"""
    
    log_count_config = f"\n        options.logCount = {config['log_count']};" if config['log_count'] else ""
    
    template = f'''package com.fjdynamics.logger.module;

import com.fjdynamics.logger.log.FjLog;

/**
 * {config['comment']} - 重构版本
 * 使用AbstractLogModule基类消除代码重复，保持完全的ABI兼容性
 * 
 * <AUTHOR>
 */
public class {class_name} extends AbstractLogModule {{
    
    // 使用LogModuleManager管理实例和配置
    private static final LogModuleManager manager = new LogModuleManager(new {class_name}());
    
    @Override
    protected String getModuleName() {{
        return {config['module_name']};
    }}
    
    @Override
    protected void configureOptions(FjLog.Options options) {{
        options.maxFileSize = {config['max_file_size']}; // 保持原有配置{log_count_config}
    }}
    
    @Override
    protected boolean shouldFlushOnSetOptions() {{
        return {str(config['should_flush']).lower()}; // 保持原有行为
    }}
    
    @Override
    public FjLog.Options create() {{
        return manager.getOrCreateOptions();
    }}
    
    // ========== 静态API方法 - 保持完全的ABI兼容性 ==========
    
    public static void setOptions(FjLog.Options option) {{
        manager.setOptions(option);
    }}

    public static void a(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.a(tag, message));
    }}

    public static void d(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.d(tag, message));
    }}

    public static void e(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.e(tag, message));
    }}

    public static void f(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.f(tag, message));
    }}

    public static void i(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.i(tag, message));
    }}

    public static void n(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.n(tag, message));
    }}

    public static void v(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.v(tag, message));
    }}

    public static void w(String tag, String message) {{
        executeLog(manager, fjLog -> fjLog.w(tag, message));
    }}

    public static void t(String message) {{
        executeLog(manager, fjLog -> fjLog.t(message));
    }}

    public static void flush() {{
        executeLog(manager, FjLog::flush);
    }}

    public static void setConsoleLogOpen(boolean consoleLogOpen) {{
        executeLog(manager, fjLog -> fjLog.setConsoleLogOpen(consoleLogOpen));
    }}
}}
'''
    return template

def refactor_module(class_name):
    """重构指定的模块"""
    if class_name not in MODULE_CONFIGS:
        print(f"未找到 {class_name} 的配置")
        return False
        
    config = MODULE_CONFIGS[class_name]
    file_path = f"Logger/src/main/java/com/fjdynamics/logger/module/{class_name}.java"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    # 生成重构后的代码
    refactored_code = generate_refactored_class(class_name, config)
    
    # 写入文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(refactored_code)
    
    print(f"✅ 已重构 {class_name}")
    return True

if __name__ == "__main__":
    print("开始批量重构日志模块...")
    
    success_count = 0
    for class_name in MODULE_CONFIGS.keys():
        if refactor_module(class_name):
            success_count += 1
    
    print(f"\n重构完成！成功重构 {success_count}/{len(MODULE_CONFIGS)} 个模块")
