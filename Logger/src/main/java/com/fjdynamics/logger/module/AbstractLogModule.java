package com.fjdynamics.logger.module;

import android.util.Log;

import com.fjdynamics.logger.BuildConfig;
import com.fjdynamics.logger.log.BaseFjLogFactory;
import com.fjdynamics.logger.log.FjLog;
import com.tencent.mars.xlog.Xlog;

/**
 * 抽象日志模块基类
 * 提供通用的日志模块实现，消除代码重复
 * 每个具体的日志模块类应该继承此类并提供静态方法的实现
 *
 * <AUTHOR>
 */
public abstract class AbstractLogModule extends BaseFjLogFactory {

    /**
     * 日志模块管理器，用于管理每个模块的FjLog实例和配置
     */
    protected static class LogModuleManager {
        private volatile FjLog fjLog;
        private volatile FjLog.Options options;
        private final AbstractLogModule factory;

        public LogModuleManager(AbstractLogModule factory) {
            this.factory = factory;
        }

        public FjLog.Options getOrCreateOptions() {
            if (options == null) {
                synchronized (this) {
                    if (options == null) {
                        options = FjLog.Options.createDefaultOptions(factory.getModuleName(), BuildConfig.DEBUG);
                        factory.configureOptions(options);
                        Log.d(Xlog.TAG, "createModel:" + factory.getClass().getSimpleName() + " options:" + options);
                    }
                }
            }
            return options;
        }

        public void setOptions(FjLog.Options newOptions) {
            synchronized (this) {
                options = newOptions;
                if (fjLog != null && factory.shouldFlushOnSetOptions()) {
                    fjLog.flush();
                    fjLog = null; // 强制重新初始化
                }
            }
        }

        public FjLog getFjLogInstance() {
            if (fjLog == null) {
                synchronized (this) {
                    if (fjLog == null) {
                        fjLog = FjLog.createFjLog(factory);
                    }
                }
            }
            return fjLog;
        }
    }
    
    /**
     * 获取模块名称 - 子类必须实现
     * @return 模块名称，对应DefaultModule中的常量
     */
    protected abstract String getModuleName();

    /**
     * 配置模块特定的选项 - 子类可以重写
     * @param options 默认选项
     */
    protected void configureOptions(FjLog.Options options) {
        // 默认配置，子类可以重写
        options.maxFileSize = 300 * 1024; // 300KB
    }

    /**
     * 是否在setOptions时需要刷新和重新初始化 - 子类可以重写
     * @return true表示需要刷新，false表示不需要
     */
    protected boolean shouldFlushOnSetOptions() {
        return false;
    }
    
    @Override
    public FjLog.Options create() {
        // 这个方法由LogModuleManager调用，子类可以重写
        return getOrCreateOptions();
    }

    /**
     * 获取或创建选项配置
     * 子类应该通过LogModuleManager来调用此方法
     */
    protected final FjLog.Options getOrCreateOptions() {
        FjLog.Options opts = FjLog.Options.createDefaultOptions(getModuleName(), BuildConfig.DEBUG);
        configureOptions(opts);
        Log.d(Xlog.TAG, "createModel:" + this.getClass().getSimpleName() + " options:" + opts);
        return opts;
    }
    
    /**
     * 通用的日志方法实现
     * 子类可以使用这些方法来实现静态日志方法
     */

    /**
     * 执行日志记录的通用方法
     * @param manager 日志管理器实例
     * @param logAction 具体的日志操作
     */
    protected static void executeLog(LogModuleManager manager, LogAction logAction) {
        FjLog fjLog = manager.getFjLogInstance();
        logAction.execute(fjLog);
    }

    /**
     * 日志操作的函数式接口
     */
    @FunctionalInterface
    protected interface LogAction {
        void execute(FjLog fjLog);
    }
}
