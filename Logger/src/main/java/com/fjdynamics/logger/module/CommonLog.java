package com.fjdynamics.logger.module;

import android.util.Log;

import com.fjdynamics.logger.BuildConfig;
import com.fjdynamics.logger.log.BaseFjLogFactory;
import com.fjdynamics.logger.log.FjLog;
import com.tencent.mars.xlog.Xlog;

public class CommonLog extends BaseFjLogFactory {
    static FjLog fjLog;
    static FjLog.Options options;

    @Override
    public FjLog.Options create() {
        if (options == null) {
            options = FjLog.Options.createDefaultOptions(DefaultModule.COMMON, BuildConfig.DEBUG);
            options.maxFileSize = 1024 * 1024;
            Log.d(Xlog.TAG, "createModel:" + this.getClass().getSimpleName() + " options:" + options);
        }
        return options;
    }

    public static void setOptions(FjLog.Options option) {
        options = option;
        if (fjLog != null) {
            fjLog.flush();
            init();
        }
    }

    private static void init() {
        if (fjLog == null) {
            synchronized (CommonLog.class) {
                if (fjLog == null) {
                    fjLog = FjLog.createFjLog(new CommonLog());
                }
            }
        }
    }

    public static void a(String tag, String message) {
        init();
        fjLog.a(tag, message);
    }

    public static void d(String tag, String message) {
        init();
        fjLog.d(tag, message);
    }

    public static void e(String tag, String message) {
        init();
        fjLog.e(tag, message);
    }

    public static void f(String tag, String message) {
        init();
        fjLog.f(tag, message);
    }

    public static void i(String tag, String message) {
        init();
        fjLog.i(tag, message);
    }

    public static void n(String tag, String message) {
        init();
        fjLog.n(tag, message);
    }

    public static void v(String tag, String message) {
        init();
        fjLog.v(tag, message);
    }

    public static void w(String tag, String message) {
        init();
        fjLog.w(tag, message);
    }

    public static void t(String message) {
        init();
        fjLog.t(message);
    }

    public static void flush() {
        init();
        fjLog.flush();
    }

    public static void setConsoleLogOpen(boolean consoleLogOpen) {
        init();
        fjLog.setConsoleLogOpen(consoleLogOpen);
    }
}