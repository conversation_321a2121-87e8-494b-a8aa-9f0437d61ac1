package com.fjdynamics.logger.module;

import com.fjdynamics.logger.log.FjLog;

/**
 * ISO总线日志模块 - 重构版本
 * 使用AbstractLogModule基类消除代码重复，保持完全的ABI兼容性
 * 
 * <AUTHOR>
 */
public class IsoBusLog extends AbstractLogModule {
    
    // 使用LogModuleManager管理实例和配置
    private static final LogModuleManager manager = new LogModuleManager(new IsoBusLog());
    
    @Override
    protected String getModuleName() {
        return DefaultModule.ISO_BUS;
    }
    
    @Override
    protected void configureOptions(FjLog.Options options) {
        options.maxFileSize = 10 * 1024 * 1024; // 保持原有配置
        options.logCount = 20;
    }
    
    @Override
    protected boolean shouldFlushOnSetOptions() {
        return true; // 保持原有行为
    }
    
    @Override
    public FjLog.Options create() {
        return manager.getOrCreateOptions();
    }
    
    // ========== 静态API方法 - 保持完全的ABI兼容性 ==========
    
    public static void setOptions(FjLog.Options option) {
        manager.setOptions(option);
    }

    public static void a(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.a(tag, message));
    }

    public static void d(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.d(tag, message));
    }

    public static void e(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.e(tag, message));
    }

    public static void f(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.f(tag, message));
    }

    public static void i(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.i(tag, message));
    }

    public static void n(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.n(tag, message));
    }

    public static void v(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.v(tag, message));
    }

    public static void w(String tag, String message) {
        executeLog(manager, fjLog -> fjLog.w(tag, message));
    }

    public static void t(String message) {
        executeLog(manager, fjLog -> fjLog.t(message));
    }

    public static void flush() {
        executeLog(manager, FjLog::flush);
    }

    public static void setConsoleLogOpen(boolean consoleLogOpen) {
        executeLog(manager, fjLog -> fjLog.setConsoleLogOpen(consoleLogOpen));
    }
}
