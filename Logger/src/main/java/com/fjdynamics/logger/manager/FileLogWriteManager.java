package com.fjdynamics.logger.manager;

import android.os.SystemClock;
import android.util.Log;

import com.fjdynamics.common.utils.FileUtil;
import com.fjdynamics.common.utils.IoUtil;
import com.fjdynamics.common.utils.LogUtil;
import com.fjdynamics.logger.bean.CacheWriteBean;
import com.fjdynamics.logger.bean.FileWriteBean;
import com.fjdynamics.logger.bean.XlogWriteBean;
import com.fjdynamics.logger.log.FjLog;
import com.fjdynamics.logger.module.CommonLog;
import com.fjdynamics.logger.utils.LogNameUtil;
import com.fjdynamics.thread.ThreadManager;
import com.tencent.mars.xlog.Xlog;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 *         写文件日志采用子线程队列方式，防止对主线程产生影响
 */
public class FileLogWriteManager {
    private static final String TAG = "FileLogWriteManager";

    private static class Singleton {
        private static final FileLogWriteManager INSTANCE = new FileLogWriteManager();
    }

    public static FileLogWriteManager getInstance() {
        return FileLogWriteManager.Singleton.INSTANCE;
    }

    private static final int QUEUE_CONSUME_INTERVAL = 3;
    private static final long CHECK_LOG_MAX_FILE_INTERVAL_MS = 30_000L;
    private static final long LOG_WRITE_INTERVAL_MS = 10_000L;
    private static final long LOG_CONTENT_MAX_LENGTH = 5000;
    private static final int LOG_MAX_QUEUE_SIZE = 30;

    private final BlockingQueue<FileWriteBean> contentQueue = new LinkedBlockingQueue<>();
    private final BlockingQueue<FileWriteBean> bytesQueue = new LinkedBlockingQueue<>();
    private final BlockingQueue<XlogWriteBean> xlogQueue = new LinkedBlockingQueue<>();

    public void initWriteThread() {
        ThreadManager.newThread(() -> {
            Map<String, CacheWriteBean> bufferMap = new HashMap<>();
            while (!Thread.interrupted()) {
                try {
                    FileWriteBean bean = contentQueue.poll(QUEUE_CONSUME_INTERVAL, TimeUnit.SECONDS);
                    if (bean == null) {
                        continue;
                    }
                    CacheWriteBean writeBean = bufferMap.get(bean.module);
                    if (writeBean == null) {
                        writeBean = new CacheWriteBean();
                        writeBean.buffer = new StringBuilder();
                        bufferMap.put(bean.module, writeBean);
                    }
                    StringBuilder buffer = writeBean.buffer;
                    buffer.append(bean.content);
                    // 写入条件，满足以下其一即可
                    // 距离上一次写入超过10s
                    // 拼接的字符串长度大于5000时
                    // 队列里面累积的日志条数大于20时
                    if (SystemClock.elapsedRealtime() - writeBean.lastCheckTime > CHECK_LOG_MAX_FILE_INTERVAL_MS) {
                        checkFileMaxCount(bean.module, bean.maxModuleSize);
                        writeBean.lastCheckTime = SystemClock.elapsedRealtime();
                    }
                    if (SystemClock.elapsedRealtime() - writeBean.lastWriteTime > LOG_WRITE_INTERVAL_MS ||
                            buffer.length() > LOG_CONTENT_MAX_LENGTH || contentQueue.size() > LOG_MAX_QUEUE_SIZE) {
                        String fileName = LogNameUtil.getWriteFileName(bean.module, bean.maxFileSize);
                        FileUtil.writeStringToFile(fileName, buffer.toString(), true);
                        writeBean.buffer.setLength(0);
                        writeBean.lastWriteTime = SystemClock.elapsedRealtime();
                    }
                } catch (Exception e) {
                    // CommonLog.e(TAG, "writeContent error, " + e);
                }
            }
        }, "Log/WriteString").start();

        ThreadManager.newThread(() -> {
            Map<String, CacheWriteBean> bufferMap = new HashMap<>();
            while (!Thread.interrupted()) {
                try {
                    FileWriteBean bean = bytesQueue.poll(QUEUE_CONSUME_INTERVAL, TimeUnit.SECONDS);
                    if (bean == null) {
                        continue;
                    }
                    CacheWriteBean writeBean = bufferMap.get(bean.module);
                    if (writeBean == null) {
                        writeBean = new CacheWriteBean();
                        writeBean.stream = new ByteArrayOutputStream(5 * 1024);
                        bufferMap.put(bean.module, writeBean);
                    }
                    ByteArrayOutputStream buffer = writeBean.stream;
                    buffer.write(bean.bytes);
                    if (SystemClock.elapsedRealtime() - writeBean.lastCheckTime > CHECK_LOG_MAX_FILE_INTERVAL_MS) {
                        checkFileMaxCount(bean.module, bean.maxModuleSize);
                        writeBean.lastCheckTime = SystemClock.elapsedRealtime();
                    }
                    if (SystemClock.elapsedRealtime() - writeBean.lastWriteTime > LOG_WRITE_INTERVAL_MS ||
                            buffer.size() > LOG_CONTENT_MAX_LENGTH || bytesQueue.size() > LOG_MAX_QUEUE_SIZE) {
                        String fileName = LogNameUtil.getWriteFileName(bean.module, bean.maxFileSize);
                        byteToFile(buffer, fileName, true);
                        buffer.reset();
                        writeBean.lastWriteTime = SystemClock.elapsedRealtime();
                    }
                } catch (Exception e) {
                    // CommonLog.e(TAG, "writeBytes error, " + e);
                }
            }
        }, "Log/WriteBytes").start();

        ThreadManager.newThread(() -> {
            while (!Thread.interrupted()) {
                try {
                    XlogWriteBean bean = xlogQueue.take();
                    if (bean.type == 0) {
                        Xlog.logWrite3(bean.logInstance, bean.logLevel, bean.tag, "", "",
                                0, bean.myPid, bean.myTid, bean.mainTid, bean.bytes);
                    } else if (bean.type == 1) {
                        Xlog.logWrite2(bean.logInstance, bean.logLevel, bean.tag, "", "",
                                0, bean.myPid, bean.myTid, bean.mainTid, bean.message);
                    }
                } catch (Exception e) {
                    // CommonLog.e(TAG, "writeXlog error, " + e);
                }
            }
        }, "Log/MainXlog").start();
    }

    public void addFileLog(String module, long maxModuleSize, long maxFileSize, String content) {
        FileWriteBean fileWriteBean = new FileWriteBean();
        fileWriteBean.module = module;
        fileWriteBean.maxModuleSize = maxModuleSize;
        fileWriteBean.maxFileSize = maxFileSize;
        fileWriteBean.content = content;
        try {
            contentQueue.put(fileWriteBean);
        } catch (InterruptedException e) {
            Log.e(TAG, "addFileLog string content error, " + e);
        }
    }

    public void addFileLog(String module, long maxModuleSize, long maxFileSize, byte[] bytes) {
        FileWriteBean fileWriteBean = new FileWriteBean();
        fileWriteBean.module = module;
        fileWriteBean.maxModuleSize = maxModuleSize;
        fileWriteBean.maxFileSize = maxFileSize;
        fileWriteBean.bytes = bytes;
        try {
            bytesQueue.put(fileWriteBean);
        } catch (InterruptedException e) {
            Log.e(TAG, "addFileLog bytes content error, " + e);
        }
    }

    public void addXlogLog(long logInstance, int logLevel, String tag, int myPid, int myTid, long mainTid, int type,
            byte[] bytes, String message) {
        XlogWriteBean xlogWriteBean = new XlogWriteBean();
        xlogWriteBean.logInstance = logInstance;
        xlogWriteBean.logLevel = logLevel;
        xlogWriteBean.tag = tag;
        xlogWriteBean.myPid = myPid;
        xlogWriteBean.myTid = myTid;
        xlogWriteBean.mainTid = mainTid;
        xlogWriteBean.type = type;
        xlogWriteBean.bytes = bytes;
        xlogWriteBean.message = message;
        try {
            xlogQueue.put(xlogWriteBean);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 定期清理存储时间最长日志
     *
     * @param module  模块
     * @param maxSize 文件存储占用内存最大值
     */
    public static void checkFileMaxCount(String module, long maxSize) {
        if (maxSize < 0) {
            return;
        }
        String filePath = FjLog.LOG_PATH + module + File.separator;
        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
            return;
        }
        File[] files = file.listFiles();
        if (files == null || files.length == 0) {
            return;
        }
        long fileLen = 0;
        for (File file1 : files) {
            fileLen += file1.length();
        }
        while (fileLen > maxSize) {
            files = file.listFiles();
            if (files == null || files.length == 0) {
                break;
            }
            // 优化：使用线性遍历找到最旧文件，避免完整排序
            File oldestFile = findOldestFile(files);
            if (oldestFile != null) {
                fileLen -= oldestFile.length();
                // CommonLog.d("日志库-FileUtils:", "fileName:" + oldestFile.getName() + "-delete:"
                // + FileUtil.delete(oldestFile));
                FileUtil.delete(oldestFile);
            } else {
                fileLen = -1;
            }
        }
    }

    /**
     * 线性遍历找到最旧修改的文件
     * 时间复杂度：O(n)，比排序的O(n log n)更高效
     */
    private static File findOldestFile(File[] files) {
        if (files == null || files.length == 0) {
            return null;
        }

        File oldestFile = files[0];
        long oldestTime = oldestFile.lastModified();

        for (int i = 1; i < files.length; i++) {
            long currentTime = files[i].lastModified();
            if (currentTime < oldestTime) {
                oldestTime = currentTime;
                oldestFile = files[i];
            }
        }

        return oldestFile;
    }

    /**
     * 将Byte数组写入文件
     *
     * @param stream byte 数据
     * @param path   文件路径
     * @param append 是否追加
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static void byteToFile(ByteArrayOutputStream stream, String path, boolean append) {
        OutputStream os = null;
        try {
            // 根据绝对路径初始化文件
            File localFile = new File(path);
            if (!localFile.exists()) {
                localFile.createNewFile();
            }
            // 输出流
            os = new FileOutputStream(localFile, append);
            stream.writeTo(os);
            os.flush();
        } catch (Exception e) {
            LogUtil.e(TAG, "byteToFile error " + e.getMessage() + " path:" + path);
        } finally {
            IoUtil.closeQuietly(os);
        }
    }
}
