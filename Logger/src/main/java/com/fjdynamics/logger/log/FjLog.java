package com.fjdynamics.logger.log;

import android.annotation.SuppressLint;
import android.os.Environment;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;

import com.fjdynamics.common.utils.AppUtil;
import com.fjdynamics.common.utils.FileUtil;
import com.fjdynamics.common.utils.LogUtil;
import com.fjdynamics.ilog.ILogger;
import com.fjdynamics.logger.bean.StackInfo;
import com.fjdynamics.logger.manager.FileLogWriteManager;
import com.fjdynamics.logger.module.DefaultModule;
import com.fjdynamics.logger.utils.StackUtils;
import com.tencent.mars.xlog.Xlog;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class FjLog implements ILogger {
    public static String LOG_PATH = Environment.getExternalStorageDirectory() + "/FJ/log/";
    public static final String FILE_TAG = "file";
    public static int StackPosition = 8;

    public static final String FUNC_NAME = "FJ";
    public static String PUB_KEY =
            "84e9ba36ef2f1e912fd472c2924edb707617788dbe561e15a7564a6cbd08b7e6637d5eb9fa86b1f1b707062336c8391ae972afab36478c9766636da6e2c9be69";
    public static String CACHE_DIR = "";
    public static String UPLOAD_URL = "";
    public static Map<String, Object> PARAMS;
    public static Map<String, String> HEADERS;

    private final long logInstance;
    private boolean isByte = false;
    private boolean isHead = true;
    private Options options;
    private int targetSdkVersion;
    /**
     * Xlog 日志单条日志最大长度
     */
    private static final int MAX_LOG_LENGTH = 1024 * 14;

    static {

    }

    public FjLog(Options option) {
        if (option != null) {
            targetSdkVersion = getTargetSdkVersion();
            CheckLogCountTask.addModule(option);
            options = option;
            if (TextUtils.isEmpty(option.module)) {
                option.module = DefaultOptions.DEFAULT_MODULE;
            }
            // TODO: 2025/8/18 支持options配置是否存的是ByteArray
            if (option.module.equals(DefaultModule.RTCM) || option.module.equals(DefaultModule.BOARD_OBSERVER)
                    || option.module.equals(DefaultModule.HIGH_FREQ_LOG)) {
                isByte = true;
                isHead = false;
            }
            FileUtil.createOrExistsDir(options.logDir);
            String pubKey = option.logEncrypt ? PUB_KEY : "";
            logInstance = Xlog.newXlogInstance(option.logLevel, option.mode, CACHE_DIR,
                    options.logDir, FUNC_NAME + option.module, 0, pubKey, isHead);
            if (targetSdkVersion < 34) {
                Xlog.setConsoleLogOpen(logInstance, option.consoleLogOpen);
            }
            Xlog.setMaxAliveTime(logInstance, option.maxAliveTime);
            Xlog.setMaxFileSize(logInstance, option.maxFileSize);
        } else {
            throw new NullPointerException("请使用Options进行参数配置");
        }
        Log.d(Xlog.TAG, "create FjLog: " + option.module + " options:" + options);
        FjLogManager.getInstance().addLogger(option.module, this);
    }

    private static int getTargetSdkVersion() {
        try {
            return AppUtil.getContext().getPackageManager()
                    .getApplicationInfo(AppUtil.getContext().getPackageName(), 0).targetSdkVersion;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * 日志初始化参数设置
     */
    public static class Options {
        /**
         * 日志级别
         */
        public int logLevel;
        /**
         * 0-异步 1-同步
         */
        public int mode;
        /**
         * 缓存位置，建议使用 Context.getCacheDir() + "/xlog"
         */
        public String cacheDir;
        /**
         * 对应模块日志文件存储位置,建议使用对应模块,"ota", 在 FJ/log/ 之下的目录
         */
        public String module;
        /**
         * 对应模块日志文件存储目录，如
         */
        public String logDir;
        /**
         * 日志是否输出到控制台,建议使用是否调试参数设置 isDebug
         */
        public boolean consoleLogOpen;
        /**
         * 单个日志最大存储大小 建议300K
         */
        public long maxFileSize;
        /**
         * 单个日志最大保存时间 建议为3天
         */
        public long maxAliveTime;
        /**
         * module存储日志数量，默认100个，0表示无上限
         */
        public int logCount;
        /**
         * module存储日志内存上线，默认300MB
         */
        public long maxModuleSize;
        /**
         * 日志自动清理标记
         */
        public boolean logAutoCancel;
        /**
         * 是否打印行号
         */
        public boolean logLineNumber;
        /**
         * 日志是否加密, 只支持 xlog 写入的文本内容，message 为 byte[] 或者直接写文件的不加密
         */
        public boolean logEncrypt;

        /**
         * 创建默认Options
         *
         * @param module         对应模块日志文件存储位置,建议使用对应模块
         * @param consoleLogOpen 日志是否输出到控制台
         */
        public static Options createDefaultOptions(String module, boolean consoleLogOpen) {
            Options options = new Options();
            options.cacheDir = CACHE_DIR;
            options.module = module;
            options.consoleLogOpen = consoleLogOpen;
            options.logLevel = DefaultOptions.DEFAULT_LOG_LEVEL;
            options.mode = DefaultOptions.DEFAULT_MODE;
            options.maxAliveTime = DefaultOptions.DEFAULT_MAX_ALIVE_TIME;
            options.maxFileSize = DefaultOptions.DEFAULT_MAX_FILE_SIZE;
            options.logCount = DefaultOptions.DEFAULT_FILE_COUNT;
            options.maxModuleSize = DefaultOptions.DEFAULT_MAX_MODULE_SIZE;
            options.logAutoCancel = DefaultOptions.DEFAULT_LOG_AUTO_CANCEL;
            options.logLineNumber = DefaultOptions.DEFAULT_LOG_LINE_NUMBER;
            options.logEncrypt = DefaultOptions.DEFAULT_LOG_ENCRYPT;
            options.logDir = LOG_PATH + module;
            return options;
        }

        @Override
        public String toString() {
            return "Options{" +
                    "logLevel=" + logLevel +
                    ", mode=" + mode +
                    ", cacheDir='" + cacheDir + '\'' +
                    ", module='" + module + '\'' +
                    ", consoleLogOpen=" + consoleLogOpen +
                    ", maxFileSize=" + maxFileSize +
                    ", maxAliveTime=" + maxAliveTime +
                    ", logCount=" + logCount +
                    ", maxModuleSize=" + maxModuleSize +
                    ", logAutoCancel=" + logAutoCancel +
                    ", logLineNumber=" + logLineNumber +
                    ", logEncrypt=" + logEncrypt +
                    '}';
        }
    }

    /**
     * 日志初始化默认参数
     */
    public static class DefaultOptions {
        /**
         * 日志级别
         */
        public final static int DEFAULT_LOG_LEVEL = Xlog.LEVEL_DEBUG;
        /**
         * 0-异步 1-同步
         */
        public final static int DEFAULT_MODE = Xlog.AppednerModeAsync;
        /**
         * 缓存位置，建议使用 Context.getCacheDir() + "/xlog"
         */
        @SuppressLint("SdCardPath")
        public final static String DEFAULT_CACHE_DIR = CACHE_DIR;
        /**
         * 日志文件存储位置,建议使用对应模块
         */
        public final static String DEFAULT_MODULE = "full_log";
        /**
         * 日志是否输出到控制台
         */
        public final static boolean DEFAULT_CONSOLE_LOG_OPEN = false;
        /**
         * 单个日志最大长度
         */
        public final static long DEFAULT_MAX_FILE_SIZE = 300 * 1024;
        /**
         * 单个日志最大保存时间为3天
         */
        public final static long DEFAULT_MAX_ALIVE_TIME = Long.MAX_VALUE;
        /**
         * module存储日志文件个数，默认100
         */
        public final static int DEFAULT_FILE_COUNT = 100;
        /**
         * 单个模块存储文件最大大小
         */
        public final static long DEFAULT_MAX_MODULE_SIZE = 200 * 1024 * 1024;

        /**
         * 日志是否自动清理
         */
        public final static boolean DEFAULT_LOG_AUTO_CANCEL = true;

        /**
         * 是否打印行号
         */
        public final static boolean DEFAULT_LOG_LINE_NUMBER = false;

        /**
         * 日志是否加密
         */
        public static final boolean DEFAULT_LOG_ENCRYPT = true;
    }

    /**
     * 初始化日志缓存目录，如无特殊需求，可以不用设置
     *
     * @param cache  日志缓存目录
     * @param url    日志上传路径
     * @param params 日志上传参数
     */
    public static void init(String cache, String url, Map<String, Object> params, Map<String, String> headers) {
        if (!TextUtils.isEmpty(cache)) {
            CACHE_DIR = cache;
        }
        UPLOAD_URL = url;
        PARAMS = params;
        HEADERS = headers;
    }

    /**
     * 初始化日志存储文件目录 建议使用 Environment.getExternalStorageDirectory() + "/FJ/log/"
     *
     * @param logDir 初始化日志存储目录
     */
    public static void initLogDir(String logDir) {
        LOG_PATH = logDir;
    }

    public static void setPubKey(String pubKey) {
        PUB_KEY = pubKey;
    }

    public static synchronized FjLog createFjLog(BaseFjLogFactory factory) {
        return new FjLog(factory.create());
    }

    /**
     * 重新新建文件进行日志写入
     */
    public void flush() {
        Xlog.appenderFlush(logInstance, true);
    }

    /**
     * 设置日志是否输出到控制台
     */
    public void setConsoleLogOpen(boolean consoleLogOpen) {
        options.consoleLogOpen = consoleLogOpen;
        if (targetSdkVersion < 34) {
            Xlog.setConsoleLogOpen(logInstance, consoleLogOpen);
        }
    }

    /**
     * 重新新建文件进行日志写入
     */
    public static void flush(long logInstance) {
        Xlog.appenderFlush(logInstance, true);
    }

    /**
     * 打印 All 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void a(String tag, String message) {
        writeLog(Xlog.LEVEL_ALL, tag, message, null);
    }

    /**
     * 打印 All 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void a(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_ALL, tag, null, message);
    }

    /**
     * 打印 verbose 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    @Override
    public void v(String tag, String message) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.v(tag, message);
        }
        writeLog(Xlog.LEVEL_VERBOSE, tag, message, null);
    }

    /**
     * 打印 verbose 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void v(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_VERBOSE, tag, null, message);
    }

    /**
     * 打印 debug 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    @Override
    public void d(String tag, String message) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.d(tag, message);
        }
        writeLog(Xlog.LEVEL_DEBUG, tag, message, null);
    }

    /**
     * 打印 debug 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void d(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_DEBUG, tag, null, message);
    }

    /**
     * 打印 info 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    @Override
    public void i(String tag, String message) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.i(tag, message);
        }
        writeLog(Xlog.LEVEL_INFO, tag, message, null);
    }

    /**
     * 打印 info 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void i(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_INFO, tag, null, message);
    }

    /**
     * 打印 warning 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    @Override
    public void w(String tag, String message) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.w(tag, message);
        }
        writeLog(Xlog.LEVEL_WARNING, tag, message, null);
    }

    /**
     * 打印 warning 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void w(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_WARNING, tag, null, message);
    }

    /**
     * 打印 error 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    @Override
    public void e(String tag, String message) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.e(tag, message);
        }
        writeLog(Xlog.LEVEL_ERROR, tag, message, null);
    }

    @Override
    public void e(String tag, String message, Throwable cause) {
        if (targetSdkVersion >= 34 && options.consoleLogOpen) {
            Log.e(tag, message, cause);
        }
        writeLog(Xlog.LEVEL_ERROR, tag, message + LogUtil.getStackTraceString(cause), null);
    }

    /**
     * 打印 error 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void e(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_ERROR, tag, null, message);
    }

    /**
     * 打印 fatal 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void f(String tag, String message) {
        writeLog(Xlog.LEVEL_FATAL, tag, message, null);
    }

    /**
     * 打印 fatal 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void f(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_FATAL, tag, null, message);
    }

    /**
     * 打印 none 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void n(String tag, String message) {
        writeLog(Xlog.LEVEL_NONE, tag, message, null);
    }

    /**
     * 打印 none 级别日志
     *
     * @param tag     日志 tag
     * @param message 日志内容
     */
    public void n(String tag, byte[] message) {
        writeLog(Xlog.LEVEL_NONE, tag, null, message);
    }

    /**
     * 记录信息级别日志
     *
     * @param message 日志内容
     */
    public void t(String message) {
        writeLog(Xlog.LEVEL_ALL, "", message, null);
    }

    /**
     * 记录信息级别日志
     *
     * @param message 日志内容
     */
    public void t(byte[] message) {
        writeLog(Xlog.LEVEL_ALL, "", null, message);
    }

    private void writeLog(int logLevel, String tag, String message, byte[] bytes) {
        if (tag == null) {
            FileLogWriteManager.getInstance()
                    .addFileLog(options.module, options.maxModuleSize, options.maxFileSize, bytes);
        } else if (FILE_TAG.equals(tag)) {
            FileLogWriteManager.getInstance()
                    .addFileLog(options.module, options.maxModuleSize, options.maxFileSize, message);
        } else {
            int myTid = Process.myTid();
            if (myTid == Xlog.MAIN_TID) {
                if (isByte) {
                    splitBytes(bytes, MAX_LOG_LENGTH, bytes1 -> FileLogWriteManager.getInstance()
                            .addXlogLog(logInstance, logLevel, tag, Xlog.PID, myTid, Xlog.MAIN_TID, 0, bytes1, ""));
                } else {
                    splitString(message, MAX_LOG_LENGTH, string -> FileLogWriteManager.getInstance()
                            .addXlogLog(logInstance, logLevel, tag, Xlog.PID, myTid, Xlog.MAIN_TID, 1, null, string));
                }
            } else {
                if (isByte) {
                    splitBytes(bytes, MAX_LOG_LENGTH, bytes1 -> Xlog.logWrite3(logInstance,
                            logLevel, tag, "", "", 0, Xlog.PID, myTid, Xlog.MAIN_TID, bytes1));
                } else {
                    if (options.logLineNumber) {
                        StackInfo stackInfo = StackUtils.makeAndroidStackInfo(StackPosition);
                        splitString(message, MAX_LOG_LENGTH, string -> Xlog.logWrite2(logInstance,
                                logLevel, tag, stackInfo.fileName, stackInfo.methodName, stackInfo.lineNumber, Xlog.PID, myTid, Xlog.MAIN_TID, string));
                    } else {
                        splitString(message, MAX_LOG_LENGTH, string -> Xlog.logWrite2(logInstance,
                                logLevel, tag, "", "", 0, Xlog.PID, myTid, Xlog.MAIN_TID, string));
                    }
                }
            }
        }

    }

    public interface SplitBytesCallback {
        void onBytes(byte[] bytes);
    }

    public interface SplitStringCallback {
        void onString(String string);
    }

    private static void splitBytes(byte[] bytes, int maxLength, SplitBytesCallback callback) {
        int length = bytes.length;
        if (length <= maxLength) {
            callback.onBytes(bytes);
            return;
        }
        int index = 0;
        byte[] buffer = new byte[maxLength];
        while (index + maxLength < length) {
            System.arraycopy(bytes, index, buffer, 0, buffer.length);
            callback.onBytes(buffer);
            index += maxLength;
        }
        if (index < length) {
            buffer = new byte[length - index];
            System.arraycopy(bytes, index, buffer, 0, buffer.length);
            callback.onBytes(buffer);
        }
    }

    private static void splitString(String str, int maxLength, SplitStringCallback callback) {
        int index = 0;
        int length = str == null ? 0 : str.length();
        if (length < maxLength) {
            callback.onString(str);
            return;
        }
        while (index + maxLength < length) {
            callback.onString(str.substring(index, index + maxLength));
            index += maxLength;
        }
        if (index < length) {
            callback.onString(str.substring(index, length));
        }
    }
}
